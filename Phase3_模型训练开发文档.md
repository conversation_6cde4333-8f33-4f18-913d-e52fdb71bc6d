# Phase 3: 模型训练开发文档

**版本:** 1.0  
**日期:** 2025年8月1日  
**项目:** 学习路径推荐模型  
**依赖:** Phase 2 特征工程完成

---

## 1. 模型训练总体架构

### 1.1 核心任务定义

**二分类任务：** 预测用户对特定题目的回答正确概率 `P(correct | user, problem)`

```
输入 (X): Phase 2 生成的特征向量
- 用户全局特征: user_global_correctness, user_total_problems_attempted
- 题目全局特征: problem_global_correctness, problem_total_attempts  
- 用户-技能交叉特征: user_skill_correctness, user_skill_attempts, user_skill_last_n_correct
- 上下文特征: attempt_count, skill_id, type_encoded, answer_type_encoded

输出 (y): correct 标签 (0 或 1)
目标: 预测概率 P(correct=1 | features)
```

### 1.2 LightGBM选择理由

**技术优势：**
- **高性能：** 基于梯度提升决策树，适合表格数据
- **内存效率：** 相比XGBoost内存占用更少，训练速度更快
- **特征重要性：** 内置特征重要性分析，便于模型解释
- **防过拟合：** 丰富的正则化参数和早停机制
- **易于调参：** 参数设置相对简单，默认值表现良好

**适用性分析：**
- 教育数据通常为表格结构，LightGBM表现优异
- 特征数量适中（15个核心特征），避免维度灾难
- 支持类别特征直接输入，无需额外编码
- 训练速度快，适合快速迭代和调参

### 1.3 训练流程架构

```
Phase 3 训练流程
├── 数据加载与预处理
│   ├── 加载特征文件 (train_features.csv, test_features.csv)
│   ├── 特征选择 (基于Phase 2重要性分析)
│   ├── 标签提取 (correct列)
│   └── 训练集内部划分 (train/validation)
├── 模型配置与初始化
│   ├── LightGBM超参数设置
│   ├── 二分类任务配置
│   └── 早停和正则化设置
├── 模型训练与验证
│   ├── 训练过程监控
│   ├── 验证集性能评估
│   └── 最优模型选择
├── 模型评估与分析
│   ├── AUC性能验证 (目标 > 0.75)
│   ├── 其他指标计算
│   └── 特征重要性分析
└── 模型保存与部署准备
    ├── 模型文件保存 (model.pkl)
    ├── 元数据保存
    └── 预测接口设计
```

---

## 2. 数据准备和预处理

### 2.1 数据加载实现

```python
import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import train_test_split
from sklearn.metrics import roc_auc_score, classification_report, confusion_matrix
import pickle
import json
from typing import Tuple, Dict, List

def load_feature_data(train_file: str = "train_features.csv", 
                     test_file: str = "test_features.csv") -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    加载Phase 2生成的特征数据
    
    Args:
        train_file: 训练特征文件路径
        test_file: 测试特征文件路径
        
    Returns:
        Tuple[pd.DataFrame, pd.DataFrame]: (训练特征, 测试特征)
    """
    print("加载特征数据...")
    
    train_features = pd.read_csv(train_file)
    test_features = pd.read_csv(test_file)
    
    print(f"✓ 训练集特征: {train_features.shape}")
    print(f"✓ 测试集特征: {test_features.shape}")
    
    # 验证必要列存在
    required_columns = ['correct', 'user_id', 'problem_id', 'order_id']
    for col in required_columns:
        assert col in train_features.columns, f"训练集缺少必要列: {col}"
        assert col in test_features.columns, f"测试集缺少必要列: {col}"
    
    return train_features, test_features
```

### 2.2 特征选择策略

```python
def select_features_for_training(df: pd.DataFrame, 
                               feature_importance_file: str = "phase2_feature_report.json") -> List[str]:
    """
    基于Phase 2的特征重要性分析选择训练特征
    
    策略:
    1. 排除标识列: user_id, problem_id, order_id
    2. 排除目标列: correct
    3. 基于相关性选择Top特征
    4. 包含所有核心特征类型
    """
    # 排除的列
    exclude_columns = ['user_id', 'problem_id', 'order_id', 'correct']
    
    # 获取所有可用特征
    available_features = [col for col in df.columns if col not in exclude_columns]
    
    # 如果有特征重要性报告，优先使用Top特征
    try:
        with open(feature_importance_file, 'r', encoding='utf-8') as f:
            report = json.load(f)
        
        top_features = report['feature_importance']['top_10_features']
        
        # 确保Top特征都在可用特征中
        selected_features = [f for f in top_features if f in available_features]
        
        # 补充其他重要特征类型
        core_feature_patterns = [
            'user_global_', 'problem_global_', 'user_skill_', 
            'attempt_count', 'skill_id', '_encoded'
        ]
        
        for feature in available_features:
            if feature not in selected_features:
                for pattern in core_feature_patterns:
                    if pattern in feature:
                        selected_features.append(feature)
                        break
        
        print(f"✓ 基于重要性分析选择 {len(selected_features)} 个特征")
        print(f"  Top 5 特征: {selected_features[:5]}")
        
    except FileNotFoundError:
        # 如果没有重要性报告，使用所有可用特征
        selected_features = available_features
        print(f"✓ 使用所有可用特征: {len(selected_features)} 个")
    
    return selected_features
```

### 2.3 数据预处理和划分

```python
def prepare_training_data(train_features: pd.DataFrame, 
                         selected_features: List[str],
                         validation_size: float = 0.2,
                         random_state: int = 42) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    """
    准备训练数据并划分训练/验证集
    
    Args:
        train_features: 训练特征数据
        selected_features: 选择的特征列表
        validation_size: 验证集比例
        random_state: 随机种子
        
    Returns:
        Tuple: (X_train, X_val, y_train, y_val)
    """
    print("准备训练数据...")
    
    # 提取特征和标签
    X = train_features[selected_features].values
    y = train_features['correct'].values
    
    print(f"✓ 特征矩阵: {X.shape}")
    print(f"✓ 标签分布: {np.bincount(y)} (0: 错误, 1: 正确)")
    print(f"✓ 正确率: {y.mean():.3f}")
    
    # 划分训练/验证集
    X_train, X_val, y_train, y_val = train_test_split(
        X, y, test_size=validation_size, random_state=random_state, stratify=y
    )
    
    print(f"✓ 训练集: {X_train.shape[0]} 样本")
    print(f"✓ 验证集: {X_val.shape[0]} 样本")
    print(f"✓ 训练集正确率: {y_train.mean():.3f}")
    print(f"✓ 验证集正确率: {y_val.mean():.3f}")
    
    return X_train, X_val, y_train, y_val

def prepare_test_data(test_features: pd.DataFrame, 
                     selected_features: List[str]) -> Tuple[np.ndarray, np.ndarray]:
    """
    准备测试数据
    
    Args:
        test_features: 测试特征数据
        selected_features: 选择的特征列表
        
    Returns:
        Tuple: (X_test, y_test)
    """
    X_test = test_features[selected_features].values
    y_test = test_features['correct'].values
    
    print(f"✓ 测试集: {X_test.shape}")
    print(f"✓ 测试集正确率: {y_test.mean():.3f}")
    
    return X_test, y_test
```

---

## 3. LightGBM模型配置

### 3.1 超参数配置

```python
def get_lightgbm_params(objective: str = 'binary') -> Dict:
    """
    获取LightGBM模型超参数配置
    
    针对二分类任务优化的参数设置
    """
    params = {
        # 基本设置
        'objective': objective,          # 二分类任务
        'metric': 'auc',                # 评估指标为AUC
        'boosting_type': 'gbdt',        # 梯度提升决策树
        'num_leaves': 31,               # 叶子节点数
        'learning_rate': 0.05,          # 学习率
        'feature_fraction': 0.9,        # 特征采样比例
        
        # 防过拟合设置
        'bagging_fraction': 0.8,        # 样本采样比例
        'bagging_freq': 5,              # 采样频率
        'min_child_samples': 20,        # 叶子节点最小样本数
        'min_child_weight': 0.001,      # 叶子节点最小权重
        'reg_alpha': 0.0,               # L1正则化
        'reg_lambda': 0.0,              # L2正则化
        
        # 训练设置
        'num_iterations': 1000,         # 最大迭代次数
        'early_stopping_rounds': 100,   # 早停轮数
        'verbose': 100,                 # 日志输出频率
        'seed': 42,                     # 随机种子
        
        # 性能设置
        'num_threads': -1,              # 使用所有CPU核心
        'force_col_wise': True,         # 列式训练（小数据集）
    }
    
    return params

def get_advanced_params_for_tuning() -> Dict:
    """
    获取用于调参的高级参数配置
    """
    tuning_params = {
        # 可调参数范围
        'num_leaves': [15, 31, 63, 127],
        'learning_rate': [0.01, 0.05, 0.1, 0.2],
        'feature_fraction': [0.7, 0.8, 0.9, 1.0],
        'bagging_fraction': [0.7, 0.8, 0.9, 1.0],
        'min_child_samples': [10, 20, 30, 50],
        'reg_alpha': [0.0, 0.1, 0.5, 1.0],
        'reg_lambda': [0.0, 0.1, 0.5, 1.0],
    }
    
    return tuning_params
```

### 3.2 模型初始化和训练配置

```python
def create_lightgbm_datasets(X_train: np.ndarray, y_train: np.ndarray,
                            X_val: np.ndarray, y_val: np.ndarray,
                            feature_names: List[str]) -> Tuple[lgb.Dataset, lgb.Dataset]:
    """
    创建LightGBM数据集对象
    
    Args:
        X_train, y_train: 训练数据
        X_val, y_val: 验证数据
        feature_names: 特征名称列表
        
    Returns:
        Tuple[lgb.Dataset, lgb.Dataset]: (训练集, 验证集)
    """
    print("创建LightGBM数据集...")
    
    # 创建训练集
    train_dataset = lgb.Dataset(
        X_train, 
        label=y_train,
        feature_name=feature_names,
        free_raw_data=False
    )
    
    # 创建验证集
    val_dataset = lgb.Dataset(
        X_val,
        label=y_val,
        feature_name=feature_names,
        reference=train_dataset,  # 使用训练集作为参考
        free_raw_data=False
    )
    
    print(f"✓ 训练数据集创建完成: {len(y_train)} 样本")
    print(f"✓ 验证数据集创建完成: {len(y_val)} 样本")
    
    return train_dataset, val_dataset
```

---

## 4. 模型训练实现

### 4.1 核心训练函数

```python
def train_lightgbm_model(train_dataset: lgb.Dataset, 
                        val_dataset: lgb.Dataset,
                        params: Dict,
                        callbacks: List = None) -> lgb.Booster:
    """
    训练LightGBM模型
    
    Args:
        train_dataset: 训练数据集
        val_dataset: 验证数据集  
        params: 模型参数
        callbacks: 回调函数列表
        
    Returns:
        lgb.Booster: 训练好的模型
    """
    print("开始训练LightGBM模型...")
    print(f"模型参数: {params}")
    
    # 设置默认回调函数
    if callbacks is None:
        callbacks = [
            lgb.log_evaluation(period=100),  # 每100轮输出一次日志
            lgb.early_stopping(stopping_rounds=100)  # 早停
        ]
    
    # 训练模型
    model = lgb.train(
        params=params,
        train_set=train_dataset,
        valid_sets=[train_dataset, val_dataset],
        valid_names=['train', 'valid'],
        callbacks=callbacks
    )
    
    print(f"✓ 模型训练完成")
    print(f"✓ 最佳迭代轮数: {model.best_iteration}")
    print(f"✓ 最佳验证AUC: {model.best_score['valid']['auc']:.4f}")
    
    return model
```

### 4.2 训练过程监控

```python
import matplotlib.pyplot as plt
from typing import Optional

def plot_training_history(model: lgb.Booster, 
                         save_path: Optional[str] = "training_history.png") -> None:
    """
    绘制训练过程曲线
    
    Args:
        model: 训练好的LightGBM模型
        save_path: 图片保存路径
    """
    # 获取训练历史
    train_results = model.evals_result_
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # 绘制训练和验证AUC曲线
    epochs = range(1, len(train_results['train']['auc']) + 1)
    
    ax.plot(epochs, train_results['train']['auc'], 'b-', label='Training AUC', linewidth=2)
    ax.plot(epochs, train_results['valid']['auc'], 'r-', label='Validation AUC', linewidth=2)
    
    # 标记最佳点
    best_epoch = model.best_iteration
    best_auc = model.best_score['valid']['auc']
    ax.axvline(x=best_epoch, color='g', linestyle='--', alpha=0.7, label=f'Best Epoch: {best_epoch}')
    ax.scatter([best_epoch], [best_auc], color='red', s=100, zorder=5)
    
    # 设置图形属性
    ax.set_xlabel('Epoch')
    ax.set_ylabel('AUC Score')
    ax.set_title('LightGBM Training History')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 添加性能信息
    ax.text(0.02, 0.98, f'Best Validation AUC: {best_auc:.4f}', 
            transform=ax.transAxes, verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"✓ 训练历史图保存至: {save_path}")
    
    plt.show()
```

---

## 5. 模型评估体系

### 5.1 核心评估指标

```python
def evaluate_model_performance(model: lgb.Booster,
                             X_val: np.ndarray, y_val: np.ndarray,
                             X_test: np.ndarray, y_test: np.ndarray,
                             feature_names: List[str]) -> Dict:
    """
    全面评估模型性能

    Args:
        model: 训练好的模型
        X_val, y_val: 验证集数据
        X_test, y_test: 测试集数据
        feature_names: 特征名称

    Returns:
        Dict: 评估结果报告
    """
    print("评估模型性能...")

    evaluation_results = {}

    # 1. 验证集评估
    val_pred_proba = model.predict(X_val, num_iteration=model.best_iteration)
    val_pred = (val_pred_proba > 0.5).astype(int)

    val_auc = roc_auc_score(y_val, val_pred_proba)
    val_report = classification_report(y_val, val_pred, output_dict=True)

    evaluation_results['validation'] = {
        'auc': val_auc,
        'accuracy': val_report['accuracy'],
        'precision': val_report['1']['precision'],
        'recall': val_report['1']['recall'],
        'f1_score': val_report['1']['f1-score'],
        'classification_report': val_report
    }

    # 2. 测试集评估
    test_pred_proba = model.predict(X_test, num_iteration=model.best_iteration)
    test_pred = (test_pred_proba > 0.5).astype(int)

    test_auc = roc_auc_score(y_test, test_pred_proba)
    test_report = classification_report(y_test, test_pred, output_dict=True)

    evaluation_results['test'] = {
        'auc': test_auc,
        'accuracy': test_report['accuracy'],
        'precision': test_report['1']['precision'],
        'recall': test_report['1']['recall'],
        'f1_score': test_report['1']['f1-score'],
        'classification_report': test_report
    }

    # 3. 特征重要性
    feature_importance = model.feature_importance(importance_type='gain')
    feature_importance_dict = dict(zip(feature_names, feature_importance))
    sorted_features = sorted(feature_importance_dict.items(), key=lambda x: x[1], reverse=True)

    evaluation_results['feature_importance'] = {
        'importance_scores': feature_importance_dict,
        'top_10_features': [item[0] for item in sorted_features[:10]]
    }

    # 4. 性能总结
    print(f"\n{'='*50}")
    print("模型性能评估结果")
    print(f"{'='*50}")
    print(f"验证集 AUC: {val_auc:.4f}")
    print(f"测试集 AUC: {test_auc:.4f}")
    print(f"验证集准确率: {val_report['accuracy']:.4f}")
    print(f"测试集准确率: {test_report['accuracy']:.4f}")

    # 检查是否达到目标性能
    target_auc = 0.75
    if test_auc >= target_auc:
        print(f"✓ 模型性能达标: AUC {test_auc:.4f} >= {target_auc}")
        evaluation_results['performance_check'] = True
    else:
        print(f"⚠️ 模型性能未达标: AUC {test_auc:.4f} < {target_auc}")
        evaluation_results['performance_check'] = False

    print(f"\nTop 5 重要特征:")
    for i, (feature, importance) in enumerate(sorted_features[:5], 1):
        print(f"  {i}. {feature}: {importance:.0f}")

    return evaluation_results

def plot_roc_curve(y_true: np.ndarray, y_pred_proba: np.ndarray,
                  title: str = "ROC Curve", save_path: str = None) -> None:
    """
    绘制ROC曲线
    """
    from sklearn.metrics import roc_curve

    fpr, tpr, _ = roc_curve(y_true, y_pred_proba)
    auc_score = roc_auc_score(y_true, y_pred_proba)

    plt.figure(figsize=(8, 6))
    plt.plot(fpr, tpr, color='darkorange', lw=2, label=f'ROC curve (AUC = {auc_score:.4f})')
    plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--', label='Random')
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title(title)
    plt.legend(loc="lower right")
    plt.grid(True, alpha=0.3)

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"✓ ROC曲线保存至: {save_path}")

    plt.show()

def plot_feature_importance(model: lgb.Booster, feature_names: List[str],
                          top_n: int = 15, save_path: str = None) -> None:
    """
    绘制特征重要性图
    """
    # 获取特征重要性
    importance = model.feature_importance(importance_type='gain')
    feature_imp = pd.DataFrame({
        'feature': feature_names,
        'importance': importance
    }).sort_values('importance', ascending=False).head(top_n)

    # 绘制图形
    plt.figure(figsize=(10, 8))
    plt.barh(range(len(feature_imp)), feature_imp['importance'], color='skyblue')
    plt.yticks(range(len(feature_imp)), feature_imp['feature'])
    plt.xlabel('Feature Importance (Gain)')
    plt.title(f'Top {top_n} Feature Importance')
    plt.gca().invert_yaxis()

    # 添加数值标签
    for i, v in enumerate(feature_imp['importance']):
        plt.text(v + max(feature_imp['importance']) * 0.01, i, f'{v:.0f}',
                va='center', fontsize=9)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"✓ 特征重要性图保存至: {save_path}")

    plt.show()
```

---

## 6. 模型保存和部署准备

### 6.1 模型保存实现

```python
def save_trained_model(model: lgb.Booster,
                      feature_names: List[str],
                      evaluation_results: Dict,
                      model_path: str = "model.pkl",
                      metadata_path: str = "model_metadata.json") -> None:
    """
    保存训练好的模型和元数据

    Args:
        model: 训练好的LightGBM模型
        feature_names: 特征名称列表
        evaluation_results: 评估结果
        model_path: 模型文件保存路径
        metadata_path: 元数据保存路径
    """
    print("保存模型和元数据...")

    # 1. 保存模型文件
    with open(model_path, 'wb') as f:
        pickle.dump(model, f)
    print(f"✓ 模型保存至: {model_path}")

    # 2. 准备元数据
    metadata = {
        'model_info': {
            'model_type': 'LightGBM',
            'task_type': 'binary_classification',
            'best_iteration': int(model.best_iteration),
            'num_features': len(feature_names),
            'feature_names': feature_names
        },
        'performance_metrics': {
            'validation_auc': float(evaluation_results['validation']['auc']),
            'test_auc': float(evaluation_results['test']['auc']),
            'validation_accuracy': float(evaluation_results['validation']['accuracy']),
            'test_accuracy': float(evaluation_results['test']['accuracy']),
            'performance_check_passed': evaluation_results['performance_check']
        },
        'feature_importance': evaluation_results['feature_importance'],
        'model_parameters': model.params,
        'training_info': {
            'training_date': pd.Timestamp.now().isoformat(),
            'phase': 'Phase 3 - Model Training',
            'target_auc_threshold': 0.75
        }
    }

    # 3. 保存元数据
    with open(metadata_path, 'w', encoding='utf-8') as f:
        json.dump(metadata, f, indent=2, ensure_ascii=False)
    print(f"✓ 元数据保存至: {metadata_path}")

    return metadata

def load_trained_model(model_path: str = "model.pkl",
                      metadata_path: str = "model_metadata.json") -> Tuple[lgb.Booster, Dict]:
    """
    加载训练好的模型和元数据

    Args:
        model_path: 模型文件路径
        metadata_path: 元数据文件路径

    Returns:
        Tuple[lgb.Booster, Dict]: (模型, 元数据)
    """
    # 加载模型
    with open(model_path, 'rb') as f:
        model = pickle.load(f)

    # 加载元数据
    with open(metadata_path, 'r', encoding='utf-8') as f:
        metadata = json.load(f)

    print(f"✓ 模型加载成功: {model_path}")
    print(f"✓ 模型性能: AUC = {metadata['performance_metrics']['test_auc']:.4f}")

    return model, metadata
```

### 6.2 预测接口设计

```python
class LearningPathPredictor:
    """
    学习路径推荐预测器
    为Phase 4离线模拟提供预测接口
    """

    def __init__(self, model_path: str = "model.pkl", metadata_path: str = "model_metadata.json"):
        """
        初始化预测器

        Args:
            model_path: 模型文件路径
            metadata_path: 元数据文件路径
        """
        self.model, self.metadata = load_trained_model(model_path, metadata_path)
        self.feature_names = self.metadata['model_info']['feature_names']

    def predict_probability(self, features: np.ndarray) -> np.ndarray:
        """
        预测正确概率

        Args:
            features: 特征矩阵 [n_samples, n_features]

        Returns:
            np.ndarray: 预测概率 [n_samples]
        """
        return self.model.predict(features, num_iteration=self.model.best_iteration)

    def predict_single(self, feature_dict: Dict) -> float:
        """
        单样本预测

        Args:
            feature_dict: 特征字典 {feature_name: value}

        Returns:
            float: 预测概率
        """
        # 构建特征向量
        feature_vector = np.array([feature_dict.get(name, 0.0) for name in self.feature_names])
        feature_vector = feature_vector.reshape(1, -1)

        return self.predict_probability(feature_vector)[0]

    def get_model_info(self) -> Dict:
        """
        获取模型信息

        Returns:
            Dict: 模型元数据
        """
        return self.metadata
```

---

## 7. 主流程执行函数

### 7.1 完整训练流程

```python
def execute_phase3_model_training(train_file: str = "train_features.csv",
                                test_file: str = "test_features.csv",
                                model_params: Dict = None,
                                validation_size: float = 0.2,
                                random_state: int = 42) -> Tuple[lgb.Booster, Dict]:
    """
    执行Phase 3完整的模型训练流程

    Args:
        train_file: 训练特征文件
        test_file: 测试特征文件
        model_params: 模型参数（可选）
        validation_size: 验证集比例
        random_state: 随机种子

    Returns:
        Tuple[lgb.Booster, Dict]: (训练好的模型, 评估结果)
    """
    print("=" * 50)
    print("Phase 3: 模型训练 (Model Training)")
    print("=" * 50)

    try:
        # 1. 数据加载
        print("\n[步骤 1] 加载特征数据...")
        train_features, test_features = load_feature_data(train_file, test_file)

        # 2. 特征选择
        print("\n[步骤 2] 特征选择...")
        selected_features = select_features_for_training(train_features)

        # 3. 数据准备
        print("\n[步骤 3] 数据预处理和划分...")
        X_train, X_val, y_train, y_val = prepare_training_data(
            train_features, selected_features, validation_size, random_state
        )
        X_test, y_test = prepare_test_data(test_features, selected_features)

        # 4. 模型配置
        print("\n[步骤 4] 模型配置...")
        if model_params is None:
            model_params = get_lightgbm_params()

        # 5. 创建数据集
        print("\n[步骤 5] 创建LightGBM数据集...")
        train_dataset, val_dataset = create_lightgbm_datasets(
            X_train, y_train, X_val, y_val, selected_features
        )

        # 6. 模型训练
        print("\n[步骤 6] 模型训练...")
        model = train_lightgbm_model(train_dataset, val_dataset, model_params)

        # 7. 模型评估
        print("\n[步骤 7] 模型评估...")
        evaluation_results = evaluate_model_performance(
            model, X_val, y_val, X_test, y_test, selected_features
        )

        # 8. 可视化分析
        print("\n[步骤 8] 生成可视化分析...")
        plot_training_history(model, "training_history.png")
        plot_roc_curve(y_test, model.predict(X_test, num_iteration=model.best_iteration),
                      "Test Set ROC Curve", "roc_curve.png")
        plot_feature_importance(model, selected_features, save_path="feature_importance.png")

        # 9. 模型保存
        print("\n[步骤 9] 保存模型...")
        metadata = save_trained_model(model, selected_features, evaluation_results)

        print("✓ Phase 3 模型训练完成")
        print("✓ 文件已保存: model.pkl, model_metadata.json")

        return model, evaluation_results

    except Exception as e:
        print(f"✗ Phase 3 执行失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise

def print_model_quality_checklist(evaluation_results: Dict) -> None:
    """
    打印模型质量保证检查清单
    """
    print("\n" + "=" * 50)
    print("模型训练质量保证检查清单")
    print("=" * 50)

    test_auc = evaluation_results['test']['auc']
    val_auc = evaluation_results['validation']['auc']

    checklist = [
        ("特征数据成功加载", True),
        ("模型训练无错误完成", True),
        ("验证集AUC > 0.70", val_auc > 0.70),
        ("测试集AUC > 0.75 (目标)", test_auc > 0.75),
        ("验证集和测试集AUC差异 < 0.05", abs(val_auc - test_auc) < 0.05),
        ("特征重要性分析合理", len(evaluation_results['feature_importance']['top_10_features']) > 0),
        ("模型文件成功保存", True),
        ("元数据完整保存", True),
        ("可视化图表生成", True),
        ("预测接口可用", True)
    ]

    for item, status in checklist:
        status_symbol = "✓" if status else "✗"
        print(f"{status_symbol} {item}")

    all_passed = all(status for _, status in checklist)
    performance_target_met = test_auc > 0.75

    print(f"\n总体状态: {'✓ 所有检查通过' if all_passed else '⚠️ 存在需要关注的检查项'}")
    print(f"性能目标: {'✓ 已达成 AUC > 0.75' if performance_target_met else '✗ 未达成 AUC > 0.75'}")

if __name__ == "__main__":
    try:
        # 执行完整的模型训练流程
        print("开始执行学习路径推荐模型 - Phase 3 模型训练")

        model, evaluation_results = execute_phase3_model_training()

        # 打印关键统计信息
        print("\n" + "=" * 30)
        print("模型训练总结")
        print("=" * 30)
        print(f"验证集 AUC: {evaluation_results['validation']['auc']:.4f}")
        print(f"测试集 AUC: {evaluation_results['test']['auc']:.4f}")
        print(f"测试集准确率: {evaluation_results['test']['accuracy']:.4f}")
        print(f"最佳迭代轮数: {model.best_iteration}")

        # 打印质量检查清单
        print_model_quality_checklist(evaluation_results)

        # 测试预测接口
        print("\n测试预测接口...")
        predictor = LearningPathPredictor()
        print(f"✓ 预测器初始化成功")
        print(f"✓ 模型特征数: {len(predictor.feature_names)}")

        print(f"\n🎉 Phase 3 模型训练成功完成！")
        print(f"📁 生成文件: model.pkl, model_metadata.json, *.png")
        print(f"⚡ 准备就绪，可以开始 Phase 4 离线模拟")

    except Exception as e:
        print(f"\n❌ Phase 3 执行失败: {str(e)}")
        import traceback
        traceback.print_exc()
```

---

## 8. 质量保证和性能要求

### 8.1 性能目标

- **主要指标：** AUC > 0.75 (必须达成)
- **辅助指标：** 准确率 > 0.70, F1-score > 0.70
- **稳定性：** 验证集和测试集AUC差异 < 0.05
- **训练效率：** 训练时间 < 5分钟 (在标准硬件上)

### 8.2 模型可解释性

- 特征重要性分析必须合理
- Top特征应该与教育直觉一致
- 用户-技能交叉特征应该占主导地位

### 8.3 部署就绪性

- 模型文件大小 < 50MB
- 预测延迟 < 10ms (单样本)
- 支持批量预测 (1000+ samples/s)

**注意事项：**
1. 严格按照特征重要性选择训练特征
2. 使用早停机制防止过拟合
3. 保存完整的模型元数据用于追溯
4. 确保预测接口与Phase 4需求匹配
5. 所有可视化图表必须保存用于分析
```
