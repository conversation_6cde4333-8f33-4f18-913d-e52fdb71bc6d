# Phase 2: 特征工程 (Feature Engineering) 开发文档

**版本:** 1.0  
**日期:** 2025年8月1日  
**项目:** 学习路径推荐模型  
**依赖:** Phase 1 数据准备完成

---

## 1. 特征工程总体架构

### 1.1 核心设计原则

**时间一致性原则（Time Consistency Principle）**
```
对于数据集中的每一条记录 R(order_id=t)，其特征计算必须严格基于：
- 所有 order_id < t 的历史记录
- 绝不能使用 order_id >= t 的任何信息
- 确保特征计算的因果关系正确性
```

**增量计算原则（Incremental Computation Principle）**
```python
# 伪代码示例
for record in sorted_data:
    features = compute_features_based_on_history(record, history_state)
    update_history_state(record, history_state)
    save_features(record, features)
```

### 1.2 四大类特征架构

```
特征体系架构
├── 用户全局历史特征 (User Global Features)
│   ├── user_global_correctness
│   └── user_total_problems_attempted
├── 题目全局历史特征 (Problem Global Features)  
│   ├── problem_global_correctness
│   └── problem_total_attempts
├── 用户-技能交叉特征 (User-Skill Cross Features) ⭐核心⭐
│   ├── user_skill_correctness
│   ├── user_skill_attempts
│   └── user_skill_last_n_correct[1,3,5]
└── 上下文与原始特征 (Context & Raw Features)
    ├── attempt_count
    ├── skill_id (encoded)
    ├── type (encoded)
    └── answer_type (encoded)
```

### 1.3 时间复杂度优化策略

**内存状态管理**
- 使用字典结构维护用户、题目、技能的累计统计
- 采用滑动窗口数据结构处理最近N次记录
- 避免重复计算，实现O(1)特征查询

**批量处理优化**
- 按order_id顺序单次遍历数据集
- 每条记录处理时间复杂度：O(1)
- 总体时间复杂度：O(n)，其中n为记录总数

---

## 2. 用户全局历史特征实现

### 2.1 数据结构设计

```python
class UserGlobalState:
    """用户全局状态管理器"""
    def __init__(self):
        self.user_stats = {}  # {user_id: {'correct_count': int, 'total_count': int}}
    
    def get_user_global_correctness(self, user_id: int) -> float:
        """获取用户全局历史平均正确率"""
        if user_id not in self.user_stats:
            return 0.0  # 新用户默认值
        
        stats = self.user_stats[user_id]
        if stats['total_count'] == 0:
            return 0.0
        return stats['correct_count'] / stats['total_count']
    
    def get_user_total_problems_attempted(self, user_id: int) -> int:
        """获取用户总答题数"""
        if user_id not in self.user_stats:
            return 0
        return self.user_stats[user_id]['total_count']
    
    def update_user_stats(self, user_id: int, correct: int):
        """更新用户统计信息"""
        if user_id not in self.user_stats:
            self.user_stats[user_id] = {'correct_count': 0, 'total_count': 0}
        
        self.user_stats[user_id]['correct_count'] += correct
        self.user_stats[user_id]['total_count'] += 1
```

### 2.2 特征计算逻辑

**user_global_correctness 计算**
```python
def compute_user_global_correctness(user_id: int, user_state: UserGlobalState) -> float:
    """
    计算用户全局历史平均正确率
    
    公式: user_global_correctness = sum(correct) / count(attempts)
    范围: [0.0, 1.0]
    新用户: 0.0 (冷启动处理)
    """
    return user_state.get_user_global_correctness(user_id)
```

**user_total_problems_attempted 计算**
```python
def compute_user_total_problems_attempted(user_id: int, user_state: UserGlobalState) -> int:
    """
    计算用户总答题数
    
    含义: 用户历史上尝试过的题目总数（包括重复尝试）
    范围: [0, +∞)
    新用户: 0
    """
    return user_state.get_user_total_problems_attempted(user_id)
```

---

## 3. 题目全局历史特征实现

### 3.1 数据结构设计

```python
class ProblemGlobalState:
    """题目全局状态管理器"""
    def __init__(self):
        self.problem_stats = {}  # {problem_id: {'correct_count': int, 'total_count': int}}
    
    def get_problem_global_correctness(self, problem_id: int) -> float:
        """获取题目全局平均正确率（难度代理指标）"""
        if problem_id not in self.problem_stats:
            return 0.5  # 新题目默认中等难度
        
        stats = self.problem_stats[problem_id]
        if stats['total_count'] == 0:
            return 0.5
        return stats['correct_count'] / stats['total_count']
    
    def get_problem_total_attempts(self, problem_id: int) -> int:
        """获取题目被回答总次数"""
        if problem_id not in self.problem_stats:
            return 0
        return self.problem_stats[problem_id]['total_count']
    
    def update_problem_stats(self, problem_id: int, correct: int):
        """更新题目统计信息"""
        if problem_id not in self.problem_stats:
            self.problem_stats[problem_id] = {'correct_count': 0, 'total_count': 0}
        
        self.problem_stats[problem_id]['correct_count'] += correct
        self.problem_stats[problem_id]['total_count'] += 1
```

### 3.2 冷启动问题处理

**新题目特征值设定策略**
```python
DEFAULT_PROBLEM_CORRECTNESS = 0.5  # 假设新题目中等难度
DEFAULT_PROBLEM_ATTEMPTS = 0       # 新题目无历史记录

# 冷启动处理逻辑
def handle_cold_start_problem(problem_id: int, problem_state: ProblemGlobalState) -> tuple:
    """
    处理新题目的冷启动问题
    
    策略:
    1. 新题目假设为中等难度 (correctness = 0.5)
    2. 尝试次数为0，表示无历史数据
    3. 随着数据积累，特征值会自动更新
    """
    if problem_id not in problem_state.problem_stats:
        return DEFAULT_PROBLEM_CORRECTNESS, DEFAULT_PROBLEM_ATTEMPTS
    
    return (problem_state.get_problem_global_correctness(problem_id),
            problem_state.get_problem_total_attempts(problem_id))
```

---

## 4. 用户-技能交叉历史特征实现（核心重点）

### 4.1 数据结构设计

```python
from collections import deque
from typing import Dict, List, Tuple

class UserSkillState:
    """用户-技能交叉状态管理器"""
    def __init__(self):
        # {(user_id, skill_id): {'correct_count': int, 'total_count': int}}
        self.user_skill_stats = {}
        
        # {(user_id, skill_id): deque([correct_1, correct_2, ...])}
        # 使用deque实现高效的滑动窗口
        self.user_skill_recent_history = {}
    
    def get_user_skill_correctness(self, user_id: int, skill_id: int) -> float:
        """获取用户在特定技能上的历史平均正确率"""
        key = (user_id, skill_id)
        if key not in self.user_skill_stats:
            return 0.0  # 用户未接触过该技能
        
        stats = self.user_skill_stats[key]
        if stats['total_count'] == 0:
            return 0.0
        return stats['correct_count'] / stats['total_count']
    
    def get_user_skill_attempts(self, user_id: int, skill_id: int) -> int:
        """获取用户在特定技能上的历史尝试次数"""
        key = (user_id, skill_id)
        if key not in self.user_skill_stats:
            return 0
        return self.user_skill_stats[key]['total_count']
    
    def get_user_skill_last_n_correct(self, user_id: int, skill_id: int, n: int) -> List[int]:
        """获取用户在特定技能上最近N次的正确情况"""
        key = (user_id, skill_id)
        if key not in self.user_skill_recent_history:
            return []
        
        recent_history = list(self.user_skill_recent_history[key])
        return recent_history[-n:] if len(recent_history) >= n else recent_history
    
    def update_user_skill_stats(self, user_id: int, skill_id: int, correct: int):
        """更新用户-技能统计信息"""
        key = (user_id, skill_id)
        
        # 更新累计统计
        if key not in self.user_skill_stats:
            self.user_skill_stats[key] = {'correct_count': 0, 'total_count': 0}
        
        self.user_skill_stats[key]['correct_count'] += correct
        self.user_skill_stats[key]['total_count'] += 1
        
        # 更新最近历史记录（滑动窗口）
        if key not in self.user_skill_recent_history:
            self.user_skill_recent_history[key] = deque(maxlen=10)  # 保留最近10次记录
        
        self.user_skill_recent_history[key].append(correct)
```

### 4.2 滑动窗口特征实现

```python
def compute_user_skill_last_n_features(user_id: int, skill_id: int, 
                                     user_skill_state: UserSkillState) -> Dict[str, float]:
    """
    计算用户-技能最近N次正确情况特征
    
    返回:
    - user_skill_last_1_correct: 最近1次是否正确 (0/1)
    - user_skill_last_3_correct: 最近3次正确率 (0.0-1.0)
    - user_skill_last_5_correct: 最近5次正确率 (0.0-1.0)
    """
    features = {}
    
    # 最近1次
    last_1 = user_skill_state.get_user_skill_last_n_correct(user_id, skill_id, 1)
    features['user_skill_last_1_correct'] = float(last_1[0]) if last_1 else 0.0
    
    # 最近3次
    last_3 = user_skill_state.get_user_skill_last_n_correct(user_id, skill_id, 3)
    features['user_skill_last_3_correct'] = sum(last_3) / len(last_3) if last_3 else 0.0
    
    # 最近5次
    last_5 = user_skill_state.get_user_skill_last_n_correct(user_id, skill_id, 5)
    features['user_skill_last_5_correct'] = sum(last_5) / len(last_5) if last_5 else 0.0
    
    return features
```

---

## 5. 上下文与原始特征处理

### 5.1 尝试次数特征

```python
class AttemptCountState:
    """用户-题目尝试次数状态管理器"""
    def __init__(self):
        # {(user_id, problem_id): attempt_count}
        self.user_problem_attempts = {}
    
    def get_attempt_count(self, user_id: int, problem_id: int) -> int:
        """获取用户对特定题目的尝试次数"""
        key = (user_id, problem_id)
        return self.user_problem_attempts.get(key, 0)
    
    def update_attempt_count(self, user_id: int, problem_id: int):
        """更新用户-题目尝试次数"""
        key = (user_id, problem_id)
        self.user_problem_attempts[key] = self.user_problem_attempts.get(key, 0) + 1
```

### 5.2 类别特征编码

```python
from sklearn.preprocessing import LabelEncoder
import pandas as pd

class CategoricalFeatureEncoder:
    """类别特征编码器"""
    def __init__(self):
        self.encoders = {}
        self.feature_mappings = {}
    
    def fit_transform_categorical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        对类别特征进行编码
        
        处理字段:
        - skill_id: 技能ID (保持原值，作为数值特征)
        - type: 题目类型 (LabelEncoder)
        - answer_type: 答案类型 (LabelEncoder)
        """
        df_encoded = df.copy()
        
        categorical_columns = ['type', 'answer_type']
        
        for col in categorical_columns:
            if col in df_encoded.columns:
                # 处理缺失值
                df_encoded[col] = df_encoded[col].fillna('unknown')
                
                # 标签编码
                if col not in self.encoders:
                    self.encoders[col] = LabelEncoder()
                    df_encoded[f'{col}_encoded'] = self.encoders[col].fit_transform(df_encoded[col])
                    
                    # 保存映射关系
                    self.feature_mappings[col] = dict(zip(
                        self.encoders[col].classes_, 
                        self.encoders[col].transform(self.encoders[col].classes_)
                    ))
                else:
                    df_encoded[f'{col}_encoded'] = self.encoders[col].transform(df_encoded[col])
        
        return df_encoded
```

---

## 6. 特征计算的时间一致性保证

### 6.1 核心实现策略

```python
def compute_features_with_temporal_consistency(df: pd.DataFrame) -> pd.DataFrame:
    """
    确保时间一致性的特征计算主函数
    
    关键原则:
    1. 数据必须已按order_id排序
    2. 逐行处理，每行特征只基于之前的历史
    3. 处理完每行后立即更新状态
    """
    # 验证数据已排序
    assert df['order_id'].is_monotonic_increasing, "数据必须按order_id排序"
    
    # 初始化状态管理器
    user_global_state = UserGlobalState()
    problem_global_state = ProblemGlobalState()
    user_skill_state = UserSkillState()
    attempt_count_state = AttemptCountState()
    categorical_encoder = CategoricalFeatureEncoder()
    
    # 存储特征结果
    features_list = []
    
    print(f"开始计算 {len(df)} 条记录的特征...")
    
    for idx, row in df.iterrows():
        if idx % 10000 == 0:
            print(f"处理进度: {idx}/{len(df)} ({idx/len(df):.1%})")
        
        # 1. 基于当前历史状态计算特征
        features = compute_single_record_features(
            row, user_global_state, problem_global_state, 
            user_skill_state, attempt_count_state
        )
        
        features_list.append(features)
        
        # 2. 使用当前记录更新历史状态
        update_all_states(
            row, user_global_state, problem_global_state,
            user_skill_state, attempt_count_state
        )
    
    # 转换为DataFrame
    features_df = pd.DataFrame(features_list)
    
    # 添加原始特征
    features_df = add_original_features(features_df, df)
    
    # 类别特征编码
    features_df = categorical_encoder.fit_transform_categorical_features(features_df)
    
    return features_df
```

### 6.2 单条记录特征计算

```python
def compute_single_record_features(row: pd.Series, user_global_state: UserGlobalState,
                                 problem_global_state: ProblemGlobalState,
                                 user_skill_state: UserSkillState,
                                 attempt_count_state: AttemptCountState) -> Dict:
    """
    计算单条记录的所有特征
    
    注意: 此时所有状态都是基于当前记录之前的历史
    """
    user_id = int(row['user_id'])
    problem_id = int(row['problem_id'])
    skill_id = int(row['skill_id'])
    
    features = {}
    
    # 1. 用户全局特征
    features['user_global_correctness'] = user_global_state.get_user_global_correctness(user_id)
    features['user_total_problems_attempted'] = user_global_state.get_user_total_problems_attempted(user_id)
    
    # 2. 题目全局特征
    features['problem_global_correctness'] = problem_global_state.get_problem_global_correctness(problem_id)
    features['problem_total_attempts'] = problem_global_state.get_problem_total_attempts(problem_id)
    
    # 3. 用户-技能交叉特征
    features['user_skill_correctness'] = user_skill_state.get_user_skill_correctness(user_id, skill_id)
    features['user_skill_attempts'] = user_skill_state.get_user_skill_attempts(user_id, skill_id)
    
    # 4. 用户-技能最近N次特征
    last_n_features = compute_user_skill_last_n_features(user_id, skill_id, user_skill_state)
    features.update(last_n_features)
    
    # 5. 尝试次数特征
    features['attempt_count'] = attempt_count_state.get_attempt_count(user_id, problem_id)
    
    # 6. 原始特征
    features['skill_id'] = skill_id
    features['order_id'] = int(row['order_id'])
    features['user_id'] = user_id
    features['problem_id'] = problem_id
    features['correct'] = int(row['correct'])  # 标签
    
    return features

def update_all_states(row: pd.Series, user_global_state: UserGlobalState,
                     problem_global_state: ProblemGlobalState,
                     user_skill_state: UserSkillState,
                     attempt_count_state: AttemptCountState):
    """
    使用当前记录更新所有状态管理器
    """
    user_id = int(row['user_id'])
    problem_id = int(row['problem_id'])
    skill_id = int(row['skill_id'])
    correct = int(row['correct'])
    
    # 更新所有状态
    user_global_state.update_user_stats(user_id, correct)
    problem_global_state.update_problem_stats(problem_id, correct)
    user_skill_state.update_user_skill_stats(user_id, skill_id, correct)
    attempt_count_state.update_attempt_count(user_id, problem_id)

---

## 7. 特征工程质量验证

### 7.1 时间一致性验证

```python
def validate_temporal_consistency(features_df: pd.DataFrame, original_df: pd.DataFrame) -> bool:
    """
    验证特征计算的时间一致性

    检查项:
    1. 特征值的单调性（如用户总答题数应该单调递增）
    2. 逻辑一致性（如正确率应该在[0,1]范围内）
    3. 冷启动处理正确性
    """
    validation_results = {}

    # 检查1: 用户总答题数单调性
    for user_id in features_df['user_id'].unique():
        user_data = features_df[features_df['user_id'] == user_id].sort_values('order_id')
        is_monotonic = user_data['user_total_problems_attempted'].is_monotonic_increasing
        if not is_monotonic:
            validation_results[f'user_{user_id}_monotonic'] = False
            print(f"⚠️ 用户 {user_id} 的总答题数不满足单调性")

    # 检查2: 正确率范围
    correctness_features = ['user_global_correctness', 'problem_global_correctness', 'user_skill_correctness']
    for feature in correctness_features:
        if feature in features_df.columns:
            out_of_range = ((features_df[feature] < 0) | (features_df[feature] > 1)).sum()
            if out_of_range > 0:
                validation_results[f'{feature}_range'] = False
                print(f"⚠️ {feature} 有 {out_of_range} 个值超出[0,1]范围")

    # 检查3: 冷启动处理
    first_attempts = features_df.groupby('user_id').first()
    cold_start_correct = (first_attempts['user_global_correctness'] == 0.0).sum()
    print(f"✓ {cold_start_correct} 个用户的首次记录正确处理了冷启动")

    all_passed = len(validation_results) == 0
    return all_passed

def validate_feature_statistics(features_df: pd.DataFrame) -> Dict:
    """
    生成特征统计报告
    """
    stats_report = {}

    # 基本统计
    numeric_features = features_df.select_dtypes(include=[np.number]).columns
    stats_report['feature_statistics'] = {}

    for feature in numeric_features:
        if feature not in ['order_id', 'user_id', 'problem_id', 'correct']:
            stats = {
                'mean': float(features_df[feature].mean()),
                'std': float(features_df[feature].std()),
                'min': float(features_df[feature].min()),
                'max': float(features_df[feature].max()),
                'missing_count': int(features_df[feature].isnull().sum())
            }
            stats_report['feature_statistics'][feature] = stats

    # 特征覆盖度分析
    stats_report['coverage_analysis'] = {
        'total_records': len(features_df),
        'users_with_history': len(features_df[features_df['user_total_problems_attempted'] > 0]),
        'problems_with_history': len(features_df[features_df['problem_total_attempts'] > 0]),
        'user_skill_pairs_with_history': len(features_df[features_df['user_skill_attempts'] > 0])
    }

    return stats_report
```

### 7.2 特征重要性分析

```python
def analyze_feature_importance(features_df: pd.DataFrame) -> Dict:
    """
    分析特征的基本重要性指标
    """
    from scipy.stats import pearsonr

    importance_analysis = {}

    # 计算与目标变量的相关性
    target = features_df['correct']
    feature_columns = [col for col in features_df.columns
                      if col not in ['order_id', 'user_id', 'problem_id', 'correct']]

    correlations = {}
    for feature in feature_columns:
        if features_df[feature].dtype in ['int64', 'float64']:
            try:
                corr, p_value = pearsonr(features_df[feature], target)
                correlations[feature] = {
                    'correlation': float(corr),
                    'p_value': float(p_value),
                    'abs_correlation': float(abs(corr))
                }
            except:
                correlations[feature] = {'correlation': 0.0, 'p_value': 1.0, 'abs_correlation': 0.0}

    # 按绝对相关性排序
    sorted_features = sorted(correlations.items(),
                           key=lambda x: x[1]['abs_correlation'],
                           reverse=True)

    importance_analysis['feature_correlations'] = dict(sorted_features)
    importance_analysis['top_10_features'] = [item[0] for item in sorted_features[:10]]

    return importance_analysis
```

---

## 8. 主流程实现

### 8.1 完整特征工程流程

```python
def execute_phase2_feature_engineering(train_file: str = "train_data.csv",
                                     test_file: str = "test_data.csv") -> Tuple[pd.DataFrame, pd.DataFrame, Dict]:
    """
    执行Phase 2完整的特征工程流程

    Args:
        train_file: 训练集文件路径
        test_file: 测试集文件路径

    Returns:
        Tuple: (训练集特征, 测试集特征, 特征工程报告)
    """
    print("=" * 50)
    print("Phase 2: 特征工程 (Feature Engineering)")
    print("=" * 50)

    try:
        # 1. 加载数据
        print("\n[步骤 1] 加载训练和测试数据...")
        train_df = pd.read_csv(train_file)
        test_df = pd.read_csv(test_file)

        print(f"✓ 训练集: {len(train_df)} 条记录")
        print(f"✓ 测试集: {len(test_df)} 条记录")

        # 2. 验证数据排序
        print("\n[步骤 2] 验证数据时间顺序...")
        assert train_df['order_id'].is_monotonic_increasing, "训练集未按order_id排序"
        assert test_df['order_id'].is_monotonic_increasing, "测试集未按order_id排序"
        assert train_df['order_id'].max() < test_df['order_id'].min(), "存在数据穿越风险"
        print("✓ 时间顺序验证通过")

        # 3. 计算训练集特征
        print("\n[步骤 3] 计算训练集特征...")
        train_features = compute_features_with_temporal_consistency(train_df)
        print(f"✓ 训练集特征计算完成: {train_features.shape}")

        # 4. 计算测试集特征（基于训练集的最终状态）
        print("\n[步骤 4] 计算测试集特征...")
        test_features = compute_test_features_based_on_train_state(test_df, train_df)
        print(f"✓ 测试集特征计算完成: {test_features.shape}")

        # 5. 特征验证
        print("\n[步骤 5] 特征质量验证...")
        train_validation = validate_temporal_consistency(train_features, train_df)
        test_validation = validate_temporal_consistency(test_features, test_df)

        if train_validation and test_validation:
            print("✓ 特征时间一致性验证通过")
        else:
            print("⚠️ 特征验证发现问题，请检查")

        # 6. 生成特征统计报告
        print("\n[步骤 6] 生成特征统计报告...")
        train_stats = validate_feature_statistics(train_features)
        test_stats = validate_feature_statistics(test_features)
        importance_analysis = analyze_feature_importance(train_features)

        # 7. 保存特征文件
        print("\n[步骤 7] 保存特征文件...")
        train_features.to_csv("train_features.csv", index=False)
        test_features.to_csv("test_features.csv", index=False)

        # 8. 保存特征工程报告
        feature_report = {
            'train_statistics': train_stats,
            'test_statistics': test_stats,
            'feature_importance': importance_analysis,
            'validation_results': {
                'train_validation_passed': train_validation,
                'test_validation_passed': test_validation
            },
            'feature_engineering_summary': {
                'train_records': len(train_features),
                'test_records': len(test_features),
                'total_features': len([col for col in train_features.columns if col != 'correct']),
                'feature_list': [col for col in train_features.columns if col not in ['order_id', 'user_id', 'problem_id', 'correct']]
            }
        }

        with open("phase2_feature_report.json", "w", encoding='utf-8') as f:
            json.dump(feature_report, f, indent=2, ensure_ascii=False, cls=NumpyEncoder)

        print("✓ Phase 2 特征工程完成")
        print("✓ 文件已保存: train_features.csv, test_features.csv, phase2_feature_report.json")

        return train_features, test_features, feature_report

    except Exception as e:
        print(f"✗ Phase 2 执行失败: {str(e)}")
        raise

def compute_test_features_based_on_train_state(test_df: pd.DataFrame, train_df: pd.DataFrame) -> pd.DataFrame:
    """
    基于训练集的最终状态计算测试集特征

    关键: 测试集特征计算需要基于训练集积累的历史状态，
         然后在测试集内部保持时间一致性
    """
    # 首先基于训练集建立初始状态
    print("  - 基于训练集建立初始状态...")
    initial_states = build_initial_states_from_train(train_df)

    # 然后在测试集上进行增量特征计算
    print("  - 在测试集上进行增量特征计算...")
    test_features = compute_features_with_initial_state(test_df, initial_states)

    return test_features
```

---

## 9. 代码实现规范

### 9.1 函数设计模式

```python
# 状态管理器接口规范
class StateManager(ABC):
    @abstractmethod
    def get_features(self, **kwargs) -> Dict:
        """获取特征值"""
        pass

    @abstractmethod
    def update_state(self, **kwargs):
        """更新状态"""
        pass

# 特征计算器接口规范
class FeatureComputer(ABC):
    @abstractmethod
    def compute_features(self, record: pd.Series, states: Dict) -> Dict:
        """计算单条记录的特征"""
        pass
```

### 9.2 内存管理优化

```python
# 大数据处理优化策略
def optimize_memory_usage():
    """
    内存优化策略:
    1. 使用生成器处理大文件
    2. 定期清理不需要的历史状态
    3. 使用数据类型优化（int32 vs int64）
    4. 批量处理减少内存碎片
    """
    pass

# 分块处理大数据集
def process_large_dataset_in_chunks(df: pd.DataFrame, chunk_size: int = 10000):
    """分块处理大数据集，避免内存溢出"""
    for i in range(0, len(df), chunk_size):
        chunk = df.iloc[i:i+chunk_size]
        yield chunk
```

### 9.3 特征存储格式规范

```
特征文件格式规范:
├── train_features.csv
│   ├── 基础标识: order_id, user_id, problem_id
│   ├── 目标变量: correct
│   ├── 用户全局特征: user_global_*, user_total_*
│   ├── 题目全局特征: problem_global_*, problem_total_*
│   ├── 用户技能特征: user_skill_*
│   └── 上下文特征: attempt_count, *_encoded
└── test_features.csv (相同格式)
```

---

## 10. 质量保证检查清单

### 10.1 特征工程验证清单

- [ ] 数据按order_id严格排序
- [ ] 训练集最大order_id < 测试集最小order_id
- [ ] 所有正确率特征值在[0,1]范围内
- [ ] 用户总答题数满足单调递增性
- [ ] 冷启动用户特征值正确设置
- [ ] 特征计算无缺失值（除设计允许外）
- [ ] 测试集特征基于训练集状态计算
- [ ] 特征文件成功保存且格式正确
- [ ] 特征统计报告生成完整
- [ ] 特征重要性分析合理

### 10.2 性能指标要求

- **时间复杂度**: O(n)，其中n为记录总数
- **空间复杂度**: O(u + p + s)，其中u为用户数，p为题目数，s为技能数
- **处理速度**: > 1000 records/second
- **内存使用**: < 2GB for 500K records

---

## 11. 使用示例

```python
if __name__ == "__main__":
    # 执行完整的特征工程流程
    train_features, test_features, report = execute_phase2_feature_engineering()

    # 打印关键信息
    print(f"\n特征工程完成:")
    print(f"- 训练集特征: {train_features.shape}")
    print(f"- 测试集特征: {test_features.shape}")
    print(f"- 特征总数: {len(report['feature_engineering_summary']['feature_list'])}")
    print(f"- Top 5 重要特征: {report['feature_importance']['top_10_features'][:5]}")
```

**注意事项:**
1. 特征计算严格遵循时间一致性原则
2. 所有状态更新在特征计算之后进行
3. 测试集特征基于训练集的最终状态
4. 冷启动问题通过合理的默认值处理
5. 内存使用优化确保大数据集处理能力
```
