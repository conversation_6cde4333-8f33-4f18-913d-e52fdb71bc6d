# Phase 1: 数据准备与划分开发文档

**版本:** 1.0  
**日期:** 2025年8月1日  
**项目:** 学习路径推荐模型  

---

## 1. 数据加载与验证

### 1.1 数据读取规范

```python
import pandas as pd
import numpy as np
import warnings
from typing import Tuple, Dict, Any

def load_raw_data(file_path: str = "assist0910.csv") -> pd.DataFrame:
    """
    加载原始数据文件
    
    Args:
        file_path: CSV文件路径
        
    Returns:
        pd.DataFrame: 原始数据框
        
    Raises:
        FileNotFoundError: 文件不存在
        pd.errors.EmptyDataError: 文件为空
    """
    try:
        # 使用低内存模式读取，避免数据类型推断错误
        df = pd.read_csv(file_path, low_memory=False)
        print(f"✓ 成功加载数据文件: {file_path}")
        print(f"✓ 数据形状: {df.shape}")
        return df
    except FileNotFoundError:
        raise FileNotFoundError(f"数据文件未找到: {file_path}")
    except pd.errors.EmptyDataError:
        raise pd.errors.EmptyDataError("数据文件为空")
```

### 1.2 数据完整性检查

```python
def validate_data_integrity(df: pd.DataFrame) -> Dict[str, Any]:
    """
    执行数据完整性检查
    
    Args:
        df: 原始数据框
        
    Returns:
        Dict: 数据质量报告
    """
    report = {}
    
    # 1. 必需字段检查
    required_columns = [
        'order_id', 'user_id', 'problem_id', 'skill_id', 'skill_name',
        'correct', 'attempt_count', 'ms_first_response'
    ]
    
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"缺少必需字段: {missing_columns}")
    
    # 2. 缺失值分析
    missing_stats = df.isnull().sum()
    report['missing_values'] = missing_stats[missing_stats > 0].to_dict()
    
    # 3. 重复记录检查
    duplicates = df.duplicated().sum()
    report['duplicate_records'] = duplicates
    
    # 4. 数据范围检查
    report['data_ranges'] = {
        'order_id_range': (df['order_id'].min(), df['order_id'].max()),
        'user_count': df['user_id'].nunique(),
        'problem_count': df['problem_id'].nunique(),
        'skill_count': df['skill_id'].nunique(),
        'correct_values': df['correct'].value_counts().to_dict()
    }
    
    return report
```

### 1.3 数据清洗与类型转换

```python
def clean_and_convert_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    数据清洗和类型转换
    
    Args:
        df: 原始数据框
        
    Returns:
        pd.DataFrame: 清洗后的数据框
    """
    df_clean = df.copy()
    
    # 1. 关键字段类型转换
    df_clean['order_id'] = pd.to_numeric(df_clean['order_id'], errors='coerce')
    df_clean['user_id'] = pd.to_numeric(df_clean['user_id'], errors='coerce')
    df_clean['problem_id'] = pd.to_numeric(df_clean['problem_id'], errors='coerce')
    df_clean['skill_id'] = pd.to_numeric(df_clean['skill_id'], errors='coerce')
    df_clean['correct'] = pd.to_numeric(df_clean['correct'], errors='coerce')
    df_clean['attempt_count'] = pd.to_numeric(df_clean['attempt_count'], errors='coerce')
    
    # 2. 移除关键字段为空的记录
    key_columns = ['order_id', 'user_id', 'problem_id', 'skill_id', 'correct']
    df_clean = df_clean.dropna(subset=key_columns)
    
    # 3. 数据合理性检查
    # correct字段应该只包含0和1
    df_clean = df_clean[df_clean['correct'].isin([0, 1])]
    
    # attempt_count应该大于0
    df_clean = df_clean[df_clean['attempt_count'] > 0]
    
    # 4. 字符串字段处理
    if 'skill_name' in df_clean.columns:
        df_clean['skill_name'] = df_clean['skill_name'].astype(str).str.strip()
    
    print(f"✓ 数据清洗完成，保留记录数: {len(df_clean)}")
    return df_clean
```

## 2. 时间序列排序

### 2.1 排序实现

```python
def sort_by_temporal_order(df: pd.DataFrame) -> pd.DataFrame:
    """
    按时间顺序严格排序
    
    Args:
        df: 清洗后的数据框
        
    Returns:
        pd.DataFrame: 按时间排序的数据框
    """
    # 按order_id升序排列（代表时间顺序）
    df_sorted = df.sort_values('order_id', ascending=True).reset_index(drop=True)
    
    print(f"✓ 数据按order_id排序完成")
    print(f"✓ Order ID范围: {df_sorted['order_id'].min()} - {df_sorted['order_id'].max()}")
    
    return df_sorted
```

### 2.2 时间顺序验证

```python
def validate_temporal_order(df: pd.DataFrame) -> bool:
    """
    验证时间顺序的正确性
    
    Args:
        df: 排序后的数据框
        
    Returns:
        bool: 排序是否正确
    """
    # 检查order_id是否严格递增
    is_monotonic = df['order_id'].is_monotonic_increasing
    
    if is_monotonic:
        print("✓ 时间顺序验证通过")
    else:
        print("✗ 时间顺序验证失败")
        
    return is_monotonic
```

## 3. 训练测试集划分

### 3.1 数据集划分实现

```python
def split_train_test(df: pd.DataFrame, train_ratio: float = 0.8) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    按时间顺序划分训练集和测试集
    
    Args:
        df: 排序后的完整数据框
        train_ratio: 训练集比例，默认0.8
        
    Returns:
        Tuple[pd.DataFrame, pd.DataFrame]: (训练集, 测试集)
    """
    total_records = len(df)
    split_index = int(total_records * train_ratio)
    
    # 严格按时间顺序划分
    train_df = df.iloc[:split_index].copy()
    test_df = df.iloc[split_index:].copy()
    
    print(f"✓ 数据集划分完成:")
    print(f"  - 训练集: {len(train_df)} 条记录 ({len(train_df)/total_records:.1%})")
    print(f"  - 测试集: {len(test_df)} 条记录 ({len(test_df)/total_records:.1%})")
    
    return train_df, test_df
```

### 3.2 划分后统计信息

```python
def analyze_split_statistics(train_df: pd.DataFrame, test_df: pd.DataFrame) -> Dict[str, Any]:
    """
    分析训练测试集划分后的统计信息
    
    Args:
        train_df: 训练集
        test_df: 测试集
        
    Returns:
        Dict: 统计信息报告
    """
    stats = {
        'train_stats': {
            'records': len(train_df),
            'users': train_df['user_id'].nunique(),
            'problems': train_df['problem_id'].nunique(),
            'skills': train_df['skill_id'].nunique(),
            'avg_correctness': train_df['correct'].mean(),
            'order_id_range': (train_df['order_id'].min(), train_df['order_id'].max())
        },
        'test_stats': {
            'records': len(test_df),
            'users': test_df['user_id'].nunique(),
            'problems': test_df['problem_id'].nunique(),
            'skills': test_df['skill_id'].nunique(),
            'avg_correctness': test_df['correct'].mean(),
            'order_id_range': (test_df['order_id'].min(), test_df['order_id'].max())
        }
    }
    
    # 用户重叠分析
    train_users = set(train_df['user_id'].unique())
    test_users = set(test_df['user_id'].unique())
    stats['user_overlap'] = {
        'common_users': len(train_users & test_users),
        'train_only_users': len(train_users - test_users),
        'test_only_users': len(test_users - train_users)
    }
    
    return stats
```

## 4. 数据穿越防护措施

### 4.1 防护原则说明

**严格的时间边界：**
- 训练集：order_id ≤ split_order_id
- 测试集：order_id > split_order_id
- 特征工程时，每条记录只能使用其order_id之前的历史数据

### 4.2 验证函数

```python
def validate_no_data_leakage(train_df: pd.DataFrame, test_df: pd.DataFrame) -> bool:
    """
    验证是否存在数据穿越
    
    Args:
        train_df: 训练集
        test_df: 测试集
        
    Returns:
        bool: 是否通过验证
    """
    train_max_order = train_df['order_id'].max()
    test_min_order = test_df['order_id'].min()
    
    no_leakage = train_max_order < test_min_order
    
    if no_leakage:
        print("✓ 数据穿越验证通过")
        print(f"  - 训练集最大order_id: {train_max_order}")
        print(f"  - 测试集最小order_id: {test_min_order}")
    else:
        print("✗ 检测到数据穿越风险")
        
    return no_leakage
```

## 5. 主流程函数

```python
def execute_phase1_data_preparation(file_path: str = "assist0910.csv") -> Tuple[pd.DataFrame, pd.DataFrame, Dict]:
    """
    执行Phase 1完整的数据准备流程
    
    Args:
        file_path: 数据文件路径
        
    Returns:
        Tuple: (训练集, 测试集, 统计报告)
    """
    print("=" * 50)
    print("Phase 1: 数据准备与划分")
    print("=" * 50)
    
    # 1. 数据加载
    df_raw = load_raw_data(file_path)
    
    # 2. 数据验证
    integrity_report = validate_data_integrity(df_raw)
    
    # 3. 数据清洗
    df_clean = clean_and_convert_data(df_raw)
    
    # 4. 时间排序
    df_sorted = sort_by_temporal_order(df_clean)
    validate_temporal_order(df_sorted)
    
    # 5. 训练测试集划分
    train_df, test_df = split_train_test(df_sorted)
    
    # 6. 数据穿越验证
    validate_no_data_leakage(train_df, test_df)
    
    # 7. 统计分析
    split_stats = analyze_split_statistics(train_df, test_df)
    
    # 8. 保存处理后的数据
    train_df.to_csv("train_data.csv", index=False)
    test_df.to_csv("test_data.csv", index=False)
    
    print("✓ Phase 1 数据准备完成")
    print("✓ 文件已保存: train_data.csv, test_data.csv")
    
    return train_df, test_df, {'integrity': integrity_report, 'split_stats': split_stats}
```

## 6. 使用示例

```python
if __name__ == "__main__":
    # 执行完整的数据准备流程
    train_data, test_data, reports = execute_phase1_data_preparation("assist0910.csv")
    
    # 打印关键统计信息
    print("\n" + "=" * 30)
    print("数据准备总结")
    print("=" * 30)
    print(f"训练集: {len(train_data)} 条记录")
    print(f"测试集: {len(test_data)} 条记录")
    print(f"用户重叠: {reports['split_stats']['user_overlap']['common_users']} 个用户")
```

---

## 7. 质量保证检查清单

- [ ] 数据文件成功加载，无格式错误
- [ ] 关键字段无缺失值，数据类型正确
- [ ] 按order_id严格升序排列
- [ ] 训练集80%，测试集20%，比例正确
- [ ] 无数据穿越，时间边界清晰
- [ ] 训练测试集统计信息合理
- [ ] 处理后数据文件成功保存
- [ ] 所有验证函数返回True

**注意事项：**
1. 所有函数都包含详细的错误处理和日志输出
2. 数据类型转换失败的记录会被自动移除
3. 保持原始数据不变，所有操作在副本上进行
4. 生成的train_data.csv和test_data.csv将用于后续Phase 2特征工程
