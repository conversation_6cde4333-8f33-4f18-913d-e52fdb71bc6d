

---

### **学习路径推荐模型开发文档**

**版本:** 1.0
**日期:** 2025年7月31日

#### **1. 项目概述**

**1.1. 目标 (Objective)**
本项目旨在基于提供的历史学习行为数据集，开发一个推荐模型。该模型能够为指定用户(`user_id`)推荐一个最优的练习题序列(`problem_id`)，以帮助其巩固薄弱的知识技能(`skill_name`)。

**1.2. 核心挑战 (Core Challenge)**
开发过程完全基于静态的历史数据集，无实时用户交互。因此，所有推荐策略的生成与评估都必须在**离线环境**中通过**模拟**完成。

**1.3. 成功标准 (Success Criterion)**
模型的核心是计算一个**匹配度 (Matching Score)**。我们的目标是，在离线模拟测试中，为用户生成的推荐序列其**平均匹配度需大于 0.65**。

---

#### **2. 核心指标定义：匹配度 (Matching Score)**

匹配度是一个综合性指标，用于衡量向用户推荐某道题目的合适程度。它由“个性化需求度”和“题目恰当度”两部分加权构成。

**`Matching_Score = w1 * Personalized_Need + w2 * Problem_Fitness`**

- **`w1`, `w2`**: 权重超参数，可调整。**初始设定：`w1 = 0.5`, `w2 = 0.5`**。

**2.1. 个性化需求度 (Personalized Need Score)**
此分数代表用户巩固该题目所属技能的迫切程度。

- **公式:** `Personalized_Need = 1.0 - (用户u在该技能s上的历史平均正确率)`
- **解读:** 用户在某个技能上历史正确率越低，学习需求度越高。

**2.2. 题目恰当度 (Problem Fitness Score)**
此分数代表题目的难度是否处于用户的“最近发展区”。我们通过一个**机器学习模型**来预测用户做对此题的概率`P(correct)`，并基于此计算恰当度。

- **预测目标:** `P(correct | user, problem)`
- **公式:** `Problem_Fitness = 1.0 - abs(P(correct | u, p) - T)`
- **解读:**
    - `P(correct | u, p)` 是模型预测出的成功概率。
    - `T` 是我们设定的理想成功率阈值。**初始设定：`T = 0.6`**。
    - 预测的成功率越接近`T`，说明题目难度越恰当，此分值越高。

---

#### **3. 开发流程 (Development Workflow)**

##### **Phase 1: 数据准备与划分**

1.  **排序:** 将整个数据集严格按照`order_id`（或其它能代表时间顺序的列）升序排列。
2.  **划分:**
    - **训练集 (Training Set):** 取排序后的前80%数据。
    - **测试集 (Test Set):** 取排序后的后20%数据。
    *   **注意：** 这是整个项目的基石，**严禁**在模型训练和特征工程阶段使用任何测试集信息，以防止数据穿越。

##### **Phase 2: 特征工程 (Feature Engineering)**

我们的目标是为数据集中的每一行（即每一次答题记录）构建丰富的特征。所有历史特征的计算都必须基于**该行记录发生之前**的数据。

**特征列表:**

1.  **用户全局历史特征:**
    - `user_global_correctness`: 用户全局历史平均正确率。
    - `user_total_problems_attempted`: 用户总答题数。
2.  **题目全局历史特征:**
    - `problem_global_correctness`: 该题目被所有用户回答的平均正确率（难度代理）。
    - `problem_total_attempts`: 该题目被回答的总次数。
3.  **用户-技能交叉历史特征 (最重要):**
    - `user_skill_correctness`: 用户在该题目所属`skill_id`上的历史平均正确率。
    - `user_skill_attempts`: 用户在该`skill_id`上的历史尝试次数。
    - `user_skill_last_n_correct[1, 3, 5]`: 用户在该`skill_id`上最近1、3、5次回答的正确情况。
4.  **上下文与原始特征:**
    - `attempt_count`: 用户尝试该**具体**`problem_id`的次数。
    - `skill_id`, `type`, `answer_type` 等可作为类别特征直接输入模型。

##### **Phase 3: 模型训练**

1.  **任务定义:** 这是一个二分类问题。
    - **输入 (X):** Phase 2 中构建的特征。
    - **标签 (y):** `correct` 列 (1 或 0)。
2.  **模型选择:** 推荐使用 **LightGBM** 。这类GBDT模型性能强大，非常适合处理表格数据，且训练速度快。
3.  **训练过程:**
    - 使用**训练集**数据进行模型训练。
    - 可以在训练集中再划分出一部分作为验证集，用于模型调参和防止过拟合。
    - **评估指标 (Model Performance):** 使用**AUC (Area Under ROC Curve)** 作为评估模型基础性能的指标。一个AUC > 0.75的模型通常被认为是有不错区分能力的。
4.  **交付产物:** 一个训练好的模型文件（例如 `model.pkl`）。

##### **Phase 4: 离线模拟与序列生成**

这是验证我们最终目标的核心环节。我们将针对**测试集**中的用户进行模拟。

**构建核心函数:** `generate_recommendation_sequence(user_id, N)`

**函数逻辑:**
1.  **输入:** `user_id`（目标用户），`N`（需要生成的序列长度）。
2.  **初始化:**
    - 加载该`user_id`在**训练集**中积累的最终能力画像（如各`skill`的正确率等）。这是他的“初始状态”。
    - 记录该用户在**训练集**中做过的所有`problem_id`，存入`done_problems`集合。
3.  **循环生成序列 (Loop N times):**
    a. **筛选候选集:** 从总题库中，移除`done_problems`里的题目，得到候选题目池。
    b. **批量计算匹配度:**
        i.  遍历候选池中的每一道`problem_id`。
        ii. 为每一道题构建其对应的**特征向量**（基于用户**当前**的内存状态）。
        iii. 调用训练好的模型，预测出`P(correct)`。
        iv. 根据第2节定义的公式，计算出每道题的`Matching_Score`。
    c. **选出最优推荐:** 选择`Matching_Score`最高的`problem_id`，将其加入到最终的推荐序列中。
    d. **模拟反馈与更新:**
        i.  将推荐出的`problem_id`加入`done_problems`集合。
        ii. 在**测试集**中查找该用户是否真实做过这道题。
            - **是:** 获取真实的`correct`结果，用它来**更新**内存中的用户能力画像。
            - **否:** 保持用户能力画像不变。
4.  **输出:** 返回一个包含`N`个有序`problem_id`的列表。

##### **Phase 5: 最终评估**

1.  遍历**测试集**中的所有（或抽样）用户。
2.  为每个用户调用`generate_recommendation_sequence`函数，生成推荐序列。
3.  计算在所有生成的推荐序列中，所有题目的`Matching_Score`的**总平均值**。
4.  **判断标准:**
    - **`AVG(Matching_Score) > 0.65`**: 达到项目成功标准。
    - **`AVG(Matching_Score) <= 0.65`**: 未达标，需返回Phase 2或Phase 3进行迭代优化（如增加特征、调整模型参数、调整匹配度公式权重等）。

---

#### **4. 交付产物 (Deliverables)**

1.  **特征工程脚本:** 清晰的、可重现的特征生成代码。
2.  **模型文件:** 训练好的LightGBM/XGBoost模型文件。
3.  **离线模拟与评估脚本:** 包含`generate_recommendation_sequence`函数的实现，以及最终评估逻辑的代码。
4.  **开发报告:** 总结模型性能（AUC）、最终的平均匹配度分数，以及开发过程中的关键决策和发现。