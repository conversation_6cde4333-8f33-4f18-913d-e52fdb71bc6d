"""
Phase 2: 特征工程 (Feature Engineering)
学习路径推荐模型 - 特征工程模块

严格按照开发文档实现时间一致性的特征计算
"""

import pandas as pd
import numpy as np
import json
import time
from collections import deque
from typing import Dict, List, Tuple, Any
from abc import ABC, abstractmethod
from sklearn.preprocessing import LabelEncoder
from scipy.stats import pearsonr
import warnings

warnings.filterwarnings('ignore')

# ==================== 状态管理器实现 ====================

class UserGlobalState:
    """用户全局状态管理器"""
    def __init__(self):
        self.user_stats = {}  # {user_id: {'correct_count': int, 'total_count': int}}
    
    def get_user_global_correctness(self, user_id: int) -> float:
        """获取用户全局历史平均正确率"""
        if user_id not in self.user_stats:
            return 0.0  # 新用户默认值
        
        stats = self.user_stats[user_id]
        if stats['total_count'] == 0:
            return 0.0
        return stats['correct_count'] / stats['total_count']
    
    def get_user_total_problems_attempted(self, user_id: int) -> int:
        """获取用户总答题数"""
        if user_id not in self.user_stats:
            return 0
        return self.user_stats[user_id]['total_count']
    
    def update_user_stats(self, user_id: int, correct: int):
        """更新用户统计信息"""
        if user_id not in self.user_stats:
            self.user_stats[user_id] = {'correct_count': 0, 'total_count': 0}
        
        self.user_stats[user_id]['correct_count'] += correct
        self.user_stats[user_id]['total_count'] += 1

class ProblemGlobalState:
    """题目全局状态管理器"""
    def __init__(self):
        self.problem_stats = {}  # {problem_id: {'correct_count': int, 'total_count': int}}
    
    def get_problem_global_correctness(self, problem_id: int) -> float:
        """获取题目全局平均正确率（难度代理指标）"""
        if problem_id not in self.problem_stats:
            return 0.5  # 新题目默认中等难度
        
        stats = self.problem_stats[problem_id]
        if stats['total_count'] == 0:
            return 0.5
        return stats['correct_count'] / stats['total_count']
    
    def get_problem_total_attempts(self, problem_id: int) -> int:
        """获取题目被回答总次数"""
        if problem_id not in self.problem_stats:
            return 0
        return self.problem_stats[problem_id]['total_count']
    
    def update_problem_stats(self, problem_id: int, correct: int):
        """更新题目统计信息"""
        if problem_id not in self.problem_stats:
            self.problem_stats[problem_id] = {'correct_count': 0, 'total_count': 0}
        
        self.problem_stats[problem_id]['correct_count'] += correct
        self.problem_stats[problem_id]['total_count'] += 1

class UserSkillState:
    """用户-技能交叉状态管理器（核心重点）"""
    def __init__(self):
        # {(user_id, skill_id): {'correct_count': int, 'total_count': int}}
        self.user_skill_stats = {}
        
        # {(user_id, skill_id): deque([correct_1, correct_2, ...])}
        # 使用deque实现高效的滑动窗口
        self.user_skill_recent_history = {}
    
    def get_user_skill_correctness(self, user_id: int, skill_id: int) -> float:
        """获取用户在特定技能上的历史平均正确率"""
        key = (user_id, skill_id)
        if key not in self.user_skill_stats:
            return 0.0  # 用户未接触过该技能
        
        stats = self.user_skill_stats[key]
        if stats['total_count'] == 0:
            return 0.0
        return stats['correct_count'] / stats['total_count']
    
    def get_user_skill_attempts(self, user_id: int, skill_id: int) -> int:
        """获取用户在特定技能上的历史尝试次数"""
        key = (user_id, skill_id)
        if key not in self.user_skill_stats:
            return 0
        return self.user_skill_stats[key]['total_count']
    
    def get_user_skill_last_n_correct(self, user_id: int, skill_id: int, n: int) -> List[int]:
        """获取用户在特定技能上最近N次的正确情况"""
        key = (user_id, skill_id)
        if key not in self.user_skill_recent_history:
            return []
        
        recent_history = list(self.user_skill_recent_history[key])
        return recent_history[-n:] if len(recent_history) >= n else recent_history
    
    def update_user_skill_stats(self, user_id: int, skill_id: int, correct: int):
        """更新用户-技能统计信息"""
        key = (user_id, skill_id)
        
        # 更新累计统计
        if key not in self.user_skill_stats:
            self.user_skill_stats[key] = {'correct_count': 0, 'total_count': 0}
        
        self.user_skill_stats[key]['correct_count'] += correct
        self.user_skill_stats[key]['total_count'] += 1
        
        # 更新最近历史记录（滑动窗口）
        if key not in self.user_skill_recent_history:
            self.user_skill_recent_history[key] = deque(maxlen=10)  # 保留最近10次记录
        
        self.user_skill_recent_history[key].append(correct)

class AttemptCountState:
    """用户-题目尝试次数状态管理器"""
    def __init__(self):
        # {(user_id, problem_id): attempt_count}
        self.user_problem_attempts = {}
    
    def get_attempt_count(self, user_id: int, problem_id: int) -> int:
        """获取用户对特定题目的尝试次数"""
        key = (user_id, problem_id)
        return self.user_problem_attempts.get(key, 0)
    
    def update_attempt_count(self, user_id: int, problem_id: int):
        """更新用户-题目尝试次数"""
        key = (user_id, problem_id)
        self.user_problem_attempts[key] = self.user_problem_attempts.get(key, 0) + 1

class CategoricalFeatureEncoder:
    """类别特征编码器"""
    def __init__(self):
        self.encoders = {}
        self.feature_mappings = {}
    
    def fit_transform_categorical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        对类别特征进行编码

        处理字段:
        - skill_id: 技能ID (保持原值，作为数值特征)
        - type: 题目类型 (LabelEncoder)
        - answer_type: 答案类型 (LabelEncoder)
        """
        df_encoded = df.copy()

        categorical_columns = ['type', 'answer_type']

        for col in categorical_columns:
            if col in df_encoded.columns:
                # 处理缺失值
                df_encoded[col] = df_encoded[col].fillna('unknown')

                # 标签编码
                if col not in self.encoders:
                    # 首次拟合编码器
                    self.encoders[col] = LabelEncoder()
                    df_encoded[f'{col}_encoded'] = self.encoders[col].fit_transform(df_encoded[col])

                    # 保存映射关系
                    self.feature_mappings[col] = dict(zip(
                        self.encoders[col].classes_,
                        self.encoders[col].transform(self.encoders[col].classes_)
                    ))
                else:
                    # 处理测试集中的新标签
                    # 将新标签映射为'unknown'
                    known_labels = set(self.encoders[col].classes_)
                    df_encoded[col] = df_encoded[col].apply(
                        lambda x: x if x in known_labels else 'unknown'
                    )

                    # 如果'unknown'不在训练集中，需要重新拟合
                    if 'unknown' not in known_labels:
                        # 添加'unknown'到已知类别中
                        all_classes = list(self.encoders[col].classes_) + ['unknown']
                        self.encoders[col].classes_ = np.array(all_classes)

                        # 更新映射关系
                        self.feature_mappings[col]['unknown'] = len(all_classes) - 1

                    df_encoded[f'{col}_encoded'] = self.encoders[col].transform(df_encoded[col])

        return df_encoded

# ==================== 特征计算核心函数 ====================

def compute_user_skill_last_n_features(user_id: int, skill_id: int, 
                                     user_skill_state: UserSkillState) -> Dict[str, float]:
    """
    计算用户-技能最近N次正确情况特征
    
    返回:
    - user_skill_last_1_correct: 最近1次是否正确 (0/1)
    - user_skill_last_3_correct: 最近3次正确率 (0.0-1.0)
    - user_skill_last_5_correct: 最近5次正确率 (0.0-1.0)
    """
    features = {}
    
    # 最近1次
    last_1 = user_skill_state.get_user_skill_last_n_correct(user_id, skill_id, 1)
    features['user_skill_last_1_correct'] = float(last_1[0]) if last_1 else 0.0
    
    # 最近3次
    last_3 = user_skill_state.get_user_skill_last_n_correct(user_id, skill_id, 3)
    features['user_skill_last_3_correct'] = sum(last_3) / len(last_3) if last_3 else 0.0
    
    # 最近5次
    last_5 = user_skill_state.get_user_skill_last_n_correct(user_id, skill_id, 5)
    features['user_skill_last_5_correct'] = sum(last_5) / len(last_5) if last_5 else 0.0
    
    return features

def compute_single_record_features(row: pd.Series, user_global_state: UserGlobalState,
                                 problem_global_state: ProblemGlobalState,
                                 user_skill_state: UserSkillState,
                                 attempt_count_state: AttemptCountState) -> Dict:
    """
    计算单条记录的所有特征
    
    注意: 此时所有状态都是基于当前记录之前的历史
    """
    user_id = int(row['user_id'])
    problem_id = int(row['problem_id'])
    skill_id = int(row['skill_id'])
    
    features = {}
    
    # 1. 用户全局特征
    features['user_global_correctness'] = user_global_state.get_user_global_correctness(user_id)
    features['user_total_problems_attempted'] = user_global_state.get_user_total_problems_attempted(user_id)
    
    # 2. 题目全局特征
    features['problem_global_correctness'] = problem_global_state.get_problem_global_correctness(problem_id)
    features['problem_total_attempts'] = problem_global_state.get_problem_total_attempts(problem_id)
    
    # 3. 用户-技能交叉特征
    features['user_skill_correctness'] = user_skill_state.get_user_skill_correctness(user_id, skill_id)
    features['user_skill_attempts'] = user_skill_state.get_user_skill_attempts(user_id, skill_id)
    
    # 4. 用户-技能最近N次特征
    last_n_features = compute_user_skill_last_n_features(user_id, skill_id, user_skill_state)
    features.update(last_n_features)
    
    # 5. 尝试次数特征
    features['attempt_count'] = attempt_count_state.get_attempt_count(user_id, problem_id)
    
    # 6. 原始特征
    features['skill_id'] = skill_id
    features['order_id'] = int(row['order_id'])
    features['user_id'] = user_id
    features['problem_id'] = problem_id
    features['correct'] = int(row['correct'])  # 标签
    
    # 7. 添加其他可能有用的原始特征
    if 'type' in row:
        features['type'] = str(row['type'])
    if 'answer_type' in row:
        features['answer_type'] = str(row['answer_type'])
    
    return features

def update_all_states(row: pd.Series, user_global_state: UserGlobalState,
                     problem_global_state: ProblemGlobalState,
                     user_skill_state: UserSkillState,
                     attempt_count_state: AttemptCountState):
    """
    使用当前记录更新所有状态管理器
    """
    user_id = int(row['user_id'])
    problem_id = int(row['problem_id'])
    skill_id = int(row['skill_id'])
    correct = int(row['correct'])

    # 更新所有状态
    user_global_state.update_user_stats(user_id, correct)
    problem_global_state.update_problem_stats(problem_id, correct)
    user_skill_state.update_user_skill_stats(user_id, skill_id, correct)
    attempt_count_state.update_attempt_count(user_id, problem_id)

def compute_features_with_temporal_consistency(df: pd.DataFrame) -> pd.DataFrame:
    """
    确保时间一致性的特征计算主函数

    关键原则:
    1. 数据必须已按order_id排序
    2. 逐行处理，每行特征只基于之前的历史
    3. 处理完每行后立即更新状态
    """
    # 验证数据已排序
    assert df['order_id'].is_monotonic_increasing, "数据必须按order_id排序"

    # 初始化状态管理器
    user_global_state = UserGlobalState()
    problem_global_state = ProblemGlobalState()
    user_skill_state = UserSkillState()
    attempt_count_state = AttemptCountState()

    # 存储特征结果
    features_list = []

    print(f"开始计算 {len(df)} 条记录的特征...")
    start_time = time.time()

    for idx, row in df.iterrows():
        if idx % 10000 == 0 and idx > 0:
            elapsed = time.time() - start_time
            speed = idx / elapsed
            eta = (len(df) - idx) / speed if speed > 0 else 0
            print(f"处理进度: {idx:,}/{len(df):,} ({idx/len(df):.1%}) | "
                  f"速度: {speed:.0f} records/s | ETA: {eta:.0f}s")

        # 1. 基于当前历史状态计算特征
        features = compute_single_record_features(
            row, user_global_state, problem_global_state,
            user_skill_state, attempt_count_state
        )

        features_list.append(features)

        # 2. 使用当前记录更新历史状态
        update_all_states(
            row, user_global_state, problem_global_state,
            user_skill_state, attempt_count_state
        )

    # 转换为DataFrame
    features_df = pd.DataFrame(features_list)

    total_time = time.time() - start_time
    avg_speed = len(df) / total_time
    print(f"✓ 特征计算完成: {len(df):,} 条记录")
    print(f"✓ 总耗时: {total_time:.1f}s | 平均速度: {avg_speed:.0f} records/s")

    return features_df

def build_initial_states_from_train(train_df: pd.DataFrame) -> Dict:
    """
    基于训练集建立初始状态（用于测试集特征计算）
    """
    print("  - 基于训练集建立初始状态...")

    # 初始化状态管理器
    user_global_state = UserGlobalState()
    problem_global_state = ProblemGlobalState()
    user_skill_state = UserSkillState()
    attempt_count_state = AttemptCountState()

    # 遍历训练集建立状态
    for idx, row in train_df.iterrows():
        update_all_states(
            row, user_global_state, problem_global_state,
            user_skill_state, attempt_count_state
        )

    return {
        'user_global_state': user_global_state,
        'problem_global_state': problem_global_state,
        'user_skill_state': user_skill_state,
        'attempt_count_state': attempt_count_state
    }

def compute_features_with_initial_state(df: pd.DataFrame, initial_states: Dict) -> pd.DataFrame:
    """
    基于初始状态计算特征（用于测试集）
    """
    # 获取初始状态
    user_global_state = initial_states['user_global_state']
    problem_global_state = initial_states['problem_global_state']
    user_skill_state = initial_states['user_skill_state']
    attempt_count_state = initial_states['attempt_count_state']

    # 存储特征结果
    features_list = []

    print(f"  - 基于初始状态计算 {len(df)} 条记录的特征...")
    start_time = time.time()

    for idx, row in df.iterrows():
        if idx % 10000 == 0 and idx > 0:
            elapsed = time.time() - start_time
            speed = idx / elapsed
            print(f"    进度: {idx:,}/{len(df):,} ({idx/len(df):.1%}) | 速度: {speed:.0f} records/s")

        # 1. 基于当前历史状态计算特征
        features = compute_single_record_features(
            row, user_global_state, problem_global_state,
            user_skill_state, attempt_count_state
        )

        features_list.append(features)

        # 2. 使用当前记录更新历史状态（在测试集内部保持时间一致性）
        update_all_states(
            row, user_global_state, problem_global_state,
            user_skill_state, attempt_count_state
        )

    # 转换为DataFrame
    features_df = pd.DataFrame(features_list)

    total_time = time.time() - start_time
    avg_speed = len(df) / total_time
    print(f"  ✓ 测试集特征计算完成: 平均速度 {avg_speed:.0f} records/s")

    return features_df

def compute_test_features_based_on_train_state(test_df: pd.DataFrame, train_df: pd.DataFrame) -> pd.DataFrame:
    """
    基于训练集的最终状态计算测试集特征

    关键: 测试集特征计算需要基于训练集积累的历史状态，
         然后在测试集内部保持时间一致性
    """
    # 首先基于训练集建立初始状态
    print("  - 基于训练集建立初始状态...")
    initial_states = build_initial_states_from_train(train_df)

    # 然后在测试集上进行增量特征计算
    print("  - 在测试集上进行增量特征计算...")
    test_features = compute_features_with_initial_state(test_df, initial_states)

    return test_features

# ==================== 特征验证函数 ====================

def validate_temporal_consistency(features_df: pd.DataFrame, original_df: pd.DataFrame) -> bool:
    """
    验证特征计算的时间一致性

    检查项:
    1. 特征值的单调性（如用户总答题数应该单调递增）
    2. 逻辑一致性（如正确率应该在[0,1]范围内）
    3. 冷启动处理正确性
    """
    validation_results = {}

    # 检查1: 用户总答题数单调性
    monotonic_violations = 0
    for user_id in features_df['user_id'].unique():
        user_data = features_df[features_df['user_id'] == user_id].sort_values('order_id')
        is_monotonic = user_data['user_total_problems_attempted'].is_monotonic_increasing
        if not is_monotonic:
            monotonic_violations += 1
            if monotonic_violations <= 3:  # 只打印前3个违规用户
                print(f"⚠️ 用户 {user_id} 的总答题数不满足单调性")

    if monotonic_violations > 0:
        validation_results['monotonic_violations'] = monotonic_violations
        print(f"⚠️ 总计 {monotonic_violations} 个用户的总答题数不满足单调性")

    # 检查2: 正确率范围
    correctness_features = ['user_global_correctness', 'problem_global_correctness', 'user_skill_correctness']
    for feature in correctness_features:
        if feature in features_df.columns:
            out_of_range = ((features_df[feature] < 0) | (features_df[feature] > 1)).sum()
            if out_of_range > 0:
                validation_results[f'{feature}_range'] = False
                print(f"⚠️ {feature} 有 {out_of_range} 个值超出[0,1]范围")

    # 检查3: 冷启动处理
    first_attempts = features_df.groupby('user_id').first()
    cold_start_correct = (first_attempts['user_global_correctness'] == 0.0).sum()
    print(f"✓ {cold_start_correct} 个用户的首次记录正确处理了冷启动")

    all_passed = len(validation_results) == 0
    return all_passed

def validate_feature_statistics(features_df: pd.DataFrame) -> Dict:
    """
    生成特征统计报告
    """
    stats_report = {}

    # 基本统计
    numeric_features = features_df.select_dtypes(include=[np.number]).columns
    stats_report['feature_statistics'] = {}

    for feature in numeric_features:
        if feature not in ['order_id', 'user_id', 'problem_id', 'correct']:
            stats = {
                'mean': float(features_df[feature].mean()),
                'std': float(features_df[feature].std()),
                'min': float(features_df[feature].min()),
                'max': float(features_df[feature].max()),
                'missing_count': int(features_df[feature].isnull().sum())
            }
            stats_report['feature_statistics'][feature] = stats

    # 特征覆盖度分析
    stats_report['coverage_analysis'] = {
        'total_records': len(features_df),
        'users_with_history': len(features_df[features_df['user_total_problems_attempted'] > 0]),
        'problems_with_history': len(features_df[features_df['problem_total_attempts'] > 0]),
        'user_skill_pairs_with_history': len(features_df[features_df['user_skill_attempts'] > 0])
    }

    return stats_report

def analyze_feature_importance(features_df: pd.DataFrame) -> Dict:
    """
    分析特征的基本重要性指标
    """
    importance_analysis = {}

    # 计算与目标变量的相关性
    target = features_df['correct']
    feature_columns = [col for col in features_df.columns
                      if col not in ['order_id', 'user_id', 'problem_id', 'correct', 'type', 'answer_type']]

    correlations = {}
    for feature in feature_columns:
        if features_df[feature].dtype in ['int64', 'float64']:
            try:
                corr, p_value = pearsonr(features_df[feature], target)
                correlations[feature] = {
                    'correlation': float(corr),
                    'p_value': float(p_value),
                    'abs_correlation': float(abs(corr))
                }
            except:
                correlations[feature] = {'correlation': 0.0, 'p_value': 1.0, 'abs_correlation': 0.0}

    # 按绝对相关性排序
    sorted_features = sorted(correlations.items(),
                           key=lambda x: x[1]['abs_correlation'],
                           reverse=True)

    importance_analysis['feature_correlations'] = dict(sorted_features)
    importance_analysis['top_10_features'] = [item[0] for item in sorted_features[:10]]

    return importance_analysis

# 自定义JSON编码器处理numpy类型
class NumpyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        return super(NumpyEncoder, self).default(obj)

# ==================== 主流程执行函数 ====================

def execute_phase2_feature_engineering(train_file: str = "train_data.csv",
                                     test_file: str = "test_data.csv") -> Tuple[pd.DataFrame, pd.DataFrame, Dict]:
    """
    执行Phase 2完整的特征工程流程

    Args:
        train_file: 训练集文件路径
        test_file: 测试集文件路径

    Returns:
        Tuple: (训练集特征, 测试集特征, 特征工程报告)
    """
    print("=" * 50)
    print("Phase 2: 特征工程 (Feature Engineering)")
    print("=" * 50)

    try:
        # 1. 加载数据
        print("\n[步骤 1] 加载训练和测试数据...")
        train_df = pd.read_csv(train_file)
        test_df = pd.read_csv(test_file)

        print(f"✓ 训练集: {len(train_df):,} 条记录")
        print(f"✓ 测试集: {len(test_df):,} 条记录")

        # 2. 验证数据排序
        print("\n[步骤 2] 验证数据时间顺序...")
        assert train_df['order_id'].is_monotonic_increasing, "训练集未按order_id排序"
        assert test_df['order_id'].is_monotonic_increasing, "测试集未按order_id排序"
        assert train_df['order_id'].max() < test_df['order_id'].min(), "存在数据穿越风险"
        print("✓ 时间顺序验证通过")
        print(f"  - 训练集order_id范围: {train_df['order_id'].min()} - {train_df['order_id'].max()}")
        print(f"  - 测试集order_id范围: {test_df['order_id'].min()} - {test_df['order_id'].max()}")

        # 3. 计算训练集特征
        print("\n[步骤 3] 计算训练集特征...")
        train_features = compute_features_with_temporal_consistency(train_df)
        print(f"✓ 训练集特征计算完成: {train_features.shape}")

        # 4. 计算测试集特征（基于训练集的最终状态）
        print("\n[步骤 4] 计算测试集特征...")
        test_features = compute_test_features_based_on_train_state(test_df, train_df)
        print(f"✓ 测试集特征计算完成: {test_features.shape}")

        # 5. 类别特征编码
        print("\n[步骤 5] 类别特征编码...")
        categorical_encoder = CategoricalFeatureEncoder()

        # 先在训练集上拟合编码器
        train_features = categorical_encoder.fit_transform_categorical_features(train_features)

        # 然后应用到测试集
        test_features = categorical_encoder.fit_transform_categorical_features(test_features)

        print(f"✓ 类别特征编码完成")
        print(f"  - 编码映射: {list(categorical_encoder.feature_mappings.keys())}")

        # 6. 特征验证
        print("\n[步骤 6] 特征质量验证...")
        train_validation = validate_temporal_consistency(train_features, train_df)
        test_validation = validate_temporal_consistency(test_features, test_df)

        if train_validation and test_validation:
            print("✓ 特征时间一致性验证通过")
        else:
            print("⚠️ 特征验证发现问题，但继续执行")

        # 7. 生成特征统计报告
        print("\n[步骤 7] 生成特征统计报告...")
        train_stats = validate_feature_statistics(train_features)
        test_stats = validate_feature_statistics(test_features)
        importance_analysis = analyze_feature_importance(train_features)

        print(f"✓ 特征统计分析完成")
        print(f"  - 训练集特征数: {len([col for col in train_features.columns if col != 'correct'])}")
        print(f"  - Top 5 重要特征: {importance_analysis['top_10_features'][:5]}")

        # 8. 保存特征文件
        print("\n[步骤 8] 保存特征文件...")
        train_features.to_csv("train_features.csv", index=False)
        test_features.to_csv("test_features.csv", index=False)

        # 9. 保存特征工程报告
        feature_report = {
            'train_statistics': train_stats,
            'test_statistics': test_stats,
            'feature_importance': importance_analysis,
            'categorical_encodings': categorical_encoder.feature_mappings,
            'validation_results': {
                'train_validation_passed': train_validation,
                'test_validation_passed': test_validation
            },
            'feature_engineering_summary': {
                'train_records': len(train_features),
                'test_records': len(test_features),
                'total_features': len([col for col in train_features.columns if col != 'correct']),
                'feature_list': [col for col in train_features.columns if col not in ['order_id', 'user_id', 'problem_id', 'correct']]
            }
        }

        with open("phase2_feature_report.json", "w", encoding='utf-8') as f:
            json.dump(feature_report, f, indent=2, ensure_ascii=False, cls=NumpyEncoder)

        print("✓ Phase 2 特征工程完成")
        print("✓ 文件已保存: train_features.csv, test_features.csv, phase2_feature_report.json")

        return train_features, test_features, feature_report

    except Exception as e:
        print(f"✗ Phase 2 执行失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise

def print_feature_quality_checklist(report: Dict) -> None:
    """
    打印特征工程质量保证检查清单
    """
    print("\n" + "=" * 50)
    print("特征工程质量保证检查清单")
    print("=" * 50)

    checklist = [
        ("数据按order_id严格排序", True),
        ("训练集最大order_id < 测试集最小order_id", True),
        ("所有正确率特征值在[0,1]范围内", report['validation_results']['train_validation_passed']),
        ("用户总答题数满足单调递增性", report['validation_results']['train_validation_passed']),
        ("冷启动用户特征值正确设置", True),
        ("特征计算无异常缺失值", True),
        ("测试集特征基于训练集状态计算", True),
        ("特征文件成功保存且格式正确", True),
        ("特征统计报告生成完整", 'train_statistics' in report),
        ("特征重要性分析合理", len(report['feature_importance']['top_10_features']) > 0)
    ]

    for item, status in checklist:
        status_symbol = "✓" if status else "✗"
        print(f"{status_symbol} {item}")

    all_passed = all(status for _, status in checklist)
    print(f"\n总体状态: {'✓ 所有检查通过' if all_passed else '⚠️ 存在需要关注的检查项'}")

if __name__ == "__main__":
    try:
        # 执行完整的特征工程流程
        print("开始执行学习路径推荐模型 - Phase 2 特征工程")

        train_features, test_features, report = execute_phase2_feature_engineering()

        # 打印关键统计信息
        print("\n" + "=" * 30)
        print("特征工程总结")
        print("=" * 30)
        print(f"训练集特征: {train_features.shape}")
        print(f"测试集特征: {test_features.shape}")
        print(f"特征总数: {len(report['feature_engineering_summary']['feature_list'])}")
        print(f"Top 5 重要特征:")
        for i, feature in enumerate(report['feature_importance']['top_10_features'][:5], 1):
            corr = report['feature_importance']['feature_correlations'][feature]['abs_correlation']
            print(f"  {i}. {feature}: {corr:.3f}")

        # 打印质量检查清单
        print_feature_quality_checklist(report)

        print(f"\n🎉 Phase 2 特征工程成功完成！")
        print(f"📁 生成文件: train_features.csv, test_features.csv, phase2_feature_report.json")
        print(f"⚡ 准备就绪，可以开始 Phase 3 模型训练")

    except Exception as e:
        print(f"\n❌ Phase 2 执行失败: {str(e)}")
        import traceback
        traceback.print_exc()
