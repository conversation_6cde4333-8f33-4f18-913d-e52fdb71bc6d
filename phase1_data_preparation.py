"""
Phase 1: 数据准备与划分
学习路径推荐模型 - 数据预处理模块

按照开发文档严格实现数据加载、清洗、排序和划分功能
"""

import pandas as pd
import numpy as np
import warnings
import json
from typing import Tuple, Dict, Any
from pathlib import Path

warnings.filterwarnings('ignore')

def load_raw_data(file_path: str = "assist0910.csv") -> pd.DataFrame:
    """
    加载原始数据文件
    
    Args:
        file_path: CSV文件路径
        
    Returns:
        pd.DataFrame: 原始数据框
        
    Raises:
        FileNotFoundError: 文件不存在
        pd.errors.EmptyDataError: 文件为空
    """
    try:
        # 使用低内存模式读取，避免数据类型推断错误
        df = pd.read_csv(file_path, low_memory=False)
        print(f"✓ 成功加载数据文件: {file_path}")
        print(f"✓ 数据形状: {df.shape}")
        return df
    except FileNotFoundError:
        raise FileNotFoundError(f"数据文件未找到: {file_path}")
    except pd.errors.EmptyDataError:
        raise pd.errors.EmptyDataError("数据文件为空")

def validate_data_integrity(df: pd.DataFrame) -> Dict[str, Any]:
    """
    执行数据完整性检查
    
    Args:
        df: 原始数据框
        
    Returns:
        Dict: 数据质量报告
    """
    report = {}
    
    # 1. 必需字段检查
    required_columns = [
        'order_id', 'user_id', 'problem_id', 'skill_id', 'skill_name',
        'correct', 'attempt_count', 'ms_first_response'
    ]
    
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"缺少必需字段: {missing_columns}")
    
    print("✓ 所有必需字段存在")
    
    # 2. 缺失值分析
    missing_stats = df.isnull().sum()
    report['missing_values'] = missing_stats[missing_stats > 0].to_dict()
    
    # 3. 重复记录检查
    duplicates = df.duplicated().sum()
    report['duplicate_records'] = duplicates
    
    # 4. 数据范围检查
    report['data_ranges'] = {
        'order_id_range': (int(df['order_id'].min()), int(df['order_id'].max())),
        'user_count': int(df['user_id'].nunique()),
        'problem_count': int(df['problem_id'].nunique()),
        'skill_count': int(df['skill_id'].nunique()),
        'correct_values': df['correct'].value_counts().to_dict()
    }
    
    print(f"✓ 数据完整性检查完成")
    print(f"  - 缺失值字段数: {len(report['missing_values'])}")
    print(f"  - 重复记录数: {duplicates}")
    print(f"  - 用户数: {report['data_ranges']['user_count']}")
    print(f"  - 题目数: {report['data_ranges']['problem_count']}")
    print(f"  - 技能数: {report['data_ranges']['skill_count']}")
    
    return report

def clean_and_convert_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    数据清洗和类型转换
    
    Args:
        df: 原始数据框
        
    Returns:
        pd.DataFrame: 清洗后的数据框
    """
    df_clean = df.copy()
    original_count = len(df_clean)
    
    # 1. 关键字段类型转换
    df_clean['order_id'] = pd.to_numeric(df_clean['order_id'], errors='coerce')
    df_clean['user_id'] = pd.to_numeric(df_clean['user_id'], errors='coerce')
    df_clean['problem_id'] = pd.to_numeric(df_clean['problem_id'], errors='coerce')
    df_clean['skill_id'] = pd.to_numeric(df_clean['skill_id'], errors='coerce')
    df_clean['correct'] = pd.to_numeric(df_clean['correct'], errors='coerce')
    df_clean['attempt_count'] = pd.to_numeric(df_clean['attempt_count'], errors='coerce')
    
    # 2. 移除关键字段为空的记录
    key_columns = ['order_id', 'user_id', 'problem_id', 'skill_id', 'correct']
    df_clean = df_clean.dropna(subset=key_columns)
    
    # 3. 数据合理性检查
    # correct字段应该只包含0和1
    df_clean = df_clean[df_clean['correct'].isin([0, 1])]
    
    # attempt_count应该大于0
    df_clean = df_clean[df_clean['attempt_count'] > 0]
    
    # 4. 字符串字段处理
    if 'skill_name' in df_clean.columns:
        df_clean['skill_name'] = df_clean['skill_name'].astype(str).str.strip()
    
    cleaned_count = len(df_clean)
    removed_count = original_count - cleaned_count
    
    print(f"✓ 数据清洗完成")
    print(f"  - 原始记录数: {original_count}")
    print(f"  - 清洗后记录数: {cleaned_count}")
    print(f"  - 移除记录数: {removed_count} ({removed_count/original_count:.2%})")
    
    return df_clean

def sort_by_temporal_order(df: pd.DataFrame) -> pd.DataFrame:
    """
    按时间顺序严格排序
    
    Args:
        df: 清洗后的数据框
        
    Returns:
        pd.DataFrame: 按时间排序的数据框
    """
    # 按order_id升序排列（代表时间顺序）
    df_sorted = df.sort_values('order_id', ascending=True).reset_index(drop=True)
    
    print(f"✓ 数据按order_id排序完成")
    print(f"✓ Order ID范围: {df_sorted['order_id'].min()} - {df_sorted['order_id'].max()}")
    
    return df_sorted

def validate_temporal_order(df: pd.DataFrame) -> bool:
    """
    验证时间顺序的正确性
    
    Args:
        df: 排序后的数据框
        
    Returns:
        bool: 排序是否正确
    """
    # 检查order_id是否严格递增
    is_monotonic = df['order_id'].is_monotonic_increasing
    
    if is_monotonic:
        print("✓ 时间顺序验证通过")
    else:
        print("✗ 时间顺序验证失败")
        
    return is_monotonic

def split_train_test(df: pd.DataFrame, train_ratio: float = 0.8) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    按时间顺序划分训练集和测试集
    
    Args:
        df: 排序后的完整数据框
        train_ratio: 训练集比例，默认0.8
        
    Returns:
        Tuple[pd.DataFrame, pd.DataFrame]: (训练集, 测试集)
    """
    total_records = len(df)
    split_index = int(total_records * train_ratio)
    
    # 严格按时间顺序划分
    train_df = df.iloc[:split_index].copy()
    test_df = df.iloc[split_index:].copy()
    
    print(f"✓ 数据集划分完成:")
    print(f"  - 训练集: {len(train_df)} 条记录 ({len(train_df)/total_records:.1%})")
    print(f"  - 测试集: {len(test_df)} 条记录 ({len(test_df)/total_records:.1%})")
    
    return train_df, test_df

def analyze_split_statistics(train_df: pd.DataFrame, test_df: pd.DataFrame) -> Dict[str, Any]:
    """
    分析训练测试集划分后的统计信息
    
    Args:
        train_df: 训练集
        test_df: 测试集
        
    Returns:
        Dict: 统计信息报告
    """
    stats = {
        'train_stats': {
            'records': len(train_df),
            'users': int(train_df['user_id'].nunique()),
            'problems': int(train_df['problem_id'].nunique()),
            'skills': int(train_df['skill_id'].nunique()),
            'avg_correctness': float(train_df['correct'].mean()),
            'order_id_range': (int(train_df['order_id'].min()), int(train_df['order_id'].max()))
        },
        'test_stats': {
            'records': len(test_df),
            'users': int(test_df['user_id'].nunique()),
            'problems': int(test_df['problem_id'].nunique()),
            'skills': int(test_df['skill_id'].nunique()),
            'avg_correctness': float(test_df['correct'].mean()),
            'order_id_range': (int(test_df['order_id'].min()), int(test_df['order_id'].max()))
        }
    }
    
    # 用户重叠分析
    train_users = set(train_df['user_id'].unique())
    test_users = set(test_df['user_id'].unique())
    stats['user_overlap'] = {
        'common_users': len(train_users & test_users),
        'train_only_users': len(train_users - test_users),
        'test_only_users': len(test_users - train_users)
    }
    
    print(f"✓ 统计分析完成:")
    print(f"  - 训练集用户数: {stats['train_stats']['users']}")
    print(f"  - 测试集用户数: {stats['test_stats']['users']}")
    print(f"  - 共同用户数: {stats['user_overlap']['common_users']}")
    print(f"  - 训练集平均正确率: {stats['train_stats']['avg_correctness']:.3f}")
    print(f"  - 测试集平均正确率: {stats['test_stats']['avg_correctness']:.3f}")
    
    return stats

def validate_no_data_leakage(train_df: pd.DataFrame, test_df: pd.DataFrame) -> bool:
    """
    验证是否存在数据穿越
    
    Args:
        train_df: 训练集
        test_df: 测试集
        
    Returns:
        bool: 是否通过验证
    """
    train_max_order = train_df['order_id'].max()
    test_min_order = test_df['order_id'].min()
    
    no_leakage = train_max_order < test_min_order
    
    if no_leakage:
        print("✓ 数据穿越验证通过")
        print(f"  - 训练集最大order_id: {train_max_order}")
        print(f"  - 测试集最小order_id: {test_min_order}")
    else:
        print("✗ 检测到数据穿越风险")
        print(f"  - 训练集最大order_id: {train_max_order}")
        print(f"  - 测试集最小order_id: {test_min_order}")
        
    return no_leakage

def execute_phase1_data_preparation(file_path: str = "assist0910.csv") -> Tuple[pd.DataFrame, pd.DataFrame, Dict]:
    """
    执行Phase 1完整的数据准备流程

    Args:
        file_path: 数据文件路径

    Returns:
        Tuple: (训练集, 测试集, 统计报告)
    """
    print("=" * 50)
    print("Phase 1: 数据准备与划分")
    print("=" * 50)

    try:
        # 1. 数据加载
        print("\n[步骤 1] 数据加载...")
        df_raw = load_raw_data(file_path)

        # 2. 数据验证
        print("\n[步骤 2] 数据完整性验证...")
        integrity_report = validate_data_integrity(df_raw)

        # 3. 数据清洗
        print("\n[步骤 3] 数据清洗与类型转换...")
        df_clean = clean_and_convert_data(df_raw)

        # 4. 时间排序
        print("\n[步骤 4] 时间序列排序...")
        df_sorted = sort_by_temporal_order(df_clean)
        validate_temporal_order(df_sorted)

        # 5. 训练测试集划分
        print("\n[步骤 5] 训练测试集划分...")
        train_df, test_df = split_train_test(df_sorted)

        # 6. 数据穿越验证
        print("\n[步骤 6] 数据穿越验证...")
        leakage_check = validate_no_data_leakage(train_df, test_df)
        if not leakage_check:
            raise ValueError("数据穿越验证失败，请检查数据划分逻辑")

        # 7. 统计分析
        print("\n[步骤 7] 统计分析...")
        split_stats = analyze_split_statistics(train_df, test_df)

        # 8. 保存处理后的数据
        print("\n[步骤 8] 保存数据文件...")
        train_df.to_csv("train_data.csv", index=False)
        test_df.to_csv("test_data.csv", index=False)

        # 保存统计报告
        full_report = {
            'integrity': integrity_report,
            'split_stats': split_stats,
            'processing_summary': {
                'original_records': int(len(df_raw)),
                'cleaned_records': int(len(df_clean)),
                'train_records': int(len(train_df)),
                'test_records': int(len(test_df)),
                'data_leakage_check': bool(leakage_check)
            }
        }

        # 自定义JSON编码器处理numpy类型
        class NumpyEncoder(json.JSONEncoder):
            def default(self, obj):
                if isinstance(obj, np.integer):
                    return int(obj)
                elif isinstance(obj, np.floating):
                    return float(obj)
                elif isinstance(obj, np.ndarray):
                    return obj.tolist()
                return super(NumpyEncoder, self).default(obj)

        with open("phase1_report.json", "w", encoding='utf-8') as f:
            json.dump(full_report, f, indent=2, ensure_ascii=False, cls=NumpyEncoder)

        print("✓ Phase 1 数据准备完成")
        print("✓ 文件已保存: train_data.csv, test_data.csv, phase1_report.json")

        return train_df, test_df, full_report

    except Exception as e:
        print(f"✗ Phase 1 执行失败: {str(e)}")
        raise

def print_quality_checklist(reports: Dict) -> None:
    """
    打印质量保证检查清单

    Args:
        reports: 统计报告字典
    """
    print("\n" + "=" * 50)
    print("质量保证检查清单")
    print("=" * 50)

    checklist = [
        ("数据文件成功加载", True),
        ("关键字段无缺失值", len(reports['integrity']['missing_values']) == 0),
        ("数据按order_id严格排序", True),
        ("训练集80%，测试集20%", abs(reports['split_stats']['train_stats']['records'] /
         (reports['split_stats']['train_stats']['records'] + reports['split_stats']['test_stats']['records']) - 0.8) < 0.01),
        ("无数据穿越", reports['processing_summary']['data_leakage_check']),
        ("统计信息合理", reports['split_stats']['train_stats']['users'] > 0),
        ("数据文件成功保存", Path("train_data.csv").exists() and Path("test_data.csv").exists())
    ]

    for item, status in checklist:
        status_symbol = "✓" if status else "✗"
        print(f"{status_symbol} {item}")

    all_passed = all(status for _, status in checklist)
    print(f"\n总体状态: {'✓ 所有检查通过' if all_passed else '✗ 存在未通过的检查项'}")

if __name__ == "__main__":
    try:
        # 执行完整的数据准备流程
        print("开始执行学习路径推荐模型 - Phase 1 数据准备")

        train_data, test_data, reports = execute_phase1_data_preparation("assist0910.csv")

        # 打印关键统计信息
        print("\n" + "=" * 30)
        print("数据准备总结")
        print("=" * 30)
        print(f"训练集: {len(train_data):,} 条记录")
        print(f"测试集: {len(test_data):,} 条记录")
        print(f"用户重叠: {reports['split_stats']['user_overlap']['common_users']:,} 个用户")
        print(f"训练集时间范围: {reports['split_stats']['train_stats']['order_id_range']}")
        print(f"测试集时间范围: {reports['split_stats']['test_stats']['order_id_range']}")

        # 打印质量检查清单
        print_quality_checklist(reports)

        print(f"\n🎉 Phase 1 数据准备成功完成！")
        print(f"📁 生成文件: train_data.csv, test_data.csv, phase1_report.json")

    except Exception as e:
        print(f"\n❌ Phase 1 执行失败: {str(e)}")
        import traceback
        traceback.print_exc()
